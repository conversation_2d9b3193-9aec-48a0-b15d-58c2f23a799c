#!/usr/bin/env python3
"""
Test script for the Superior AI Agent capabilities.

This script tests the advanced AI coding capabilities including
intelligent completion, refactoring suggestions, debugging assistance,
and architecture analysis.
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from superior_ai_agent import SuperiorAIAgent, CodeQuality, RefactoringType


def test_code_quality_analysis():
    """Test code quality analysis capabilities."""
    print("🧪 Testing code quality analysis...")
    
    agent = SuperiorAIAgent()
    
    # Test Python code
    python_code = '''
def calculate_total(items):
    total = 0
    for item in items:
        total = total + item
    return total

class ShoppingCart:
    def __init__(self):
        self.items = []
    
    def add_item(self, item):
        self.items.append(item)
    
    def get_total(self):
        return calculate_total(self.items)
'''
    
    analysis = agent.analyze_code_quality(python_code, "python", "test.py")
    
    print(f"✅ Quality Score: {analysis['quality_score']:.1%}")
    print(f"✅ Functions: {analysis['metrics']['function_count']}")
    print(f"✅ Classes: {analysis['metrics']['class_count']}")
    print(f"✅ Issues: {len(analysis['issues'])}")
    print(f"✅ Suggestions: {len(analysis['suggestions'])}")
    
    return True


def test_intelligent_completion():
    """Test intelligent code completion."""
    print("\n🧪 Testing intelligent completion...")
    
    agent = SuperiorAIAgent()
    
    # Test completion contexts
    test_cases = [
        {
            "code": "def calculate_",
            "cursor": 13,
            "language": "python",
            "expected_type": "function_definition"
        },
        {
            "code": "import ",
            "cursor": 7,
            "language": "python", 
            "expected_type": "import_statement"
        },
        {
            "code": "result = ",
            "cursor": 9,
            "language": "python",
            "expected_type": "variable_assignment"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        completions = agent.generate_intelligent_completion(
            case["code"], case["cursor"], case["language"]
        )
        
        print(f"✅ Test {i}: Generated {len(completions)} completions")
        if completions:
            best_completion = completions[0]
            print(f"   Best: '{best_completion.completion_text}' ({best_completion.confidence:.1%})")
    
    return True


def test_refactoring_suggestions():
    """Test refactoring suggestions."""
    print("\n🧪 Testing refactoring suggestions...")
    
    agent = SuperiorAIAgent()
    
    # Test code with refactoring opportunities
    test_codes = [
        {
            "code": '''
def very_long_function_that_does_too_many_things():
    # This function is way too long and does multiple things
    data = []
    for i in range(100):
        if i % 2 == 0:
            data.append(i * 2)
        elif i % 3 == 0:
            data.append(i * 3)
        else:
            data.append(i)
    
    processed_data = []
    for item in data:
        if item > 50:
            processed_data.append(item * 1.1)
        else:
            processed_data.append(item * 0.9)
    
    return processed_data
''',
            "language": "python"
        },
        {
            "code": '''
var userName = "John";
var userAge = 25;
var userEmail = "<EMAIL>";

function getUserInfo() {
    return userName + " is " + userAge + " years old";
}
''',
            "language": "javascript"
        },
        {
            "code": '''
public class User {
    public String name;
    public int age;
    public String email;
    
    public void setUserData(String n, int a, String e) {
        name = n;
        age = a;
        email = e;
    }
}
''',
            "language": "java"
        }
    ]
    
    for i, test_case in enumerate(test_codes, 1):
        suggestions = agent.suggest_refactoring(
            test_case["code"], test_case["language"], f"test{i}.{test_case['language']}"
        )
        
        print(f"✅ {test_case['language'].title()} test: {len(suggestions)} suggestions")
        for suggestion in suggestions[:2]:  # Show top 2
            print(f"   - {suggestion.title} ({suggestion.confidence:.1%})")
    
    return True


def test_debugging_assistance():
    """Test debugging assistance capabilities."""
    print("\n🧪 Testing debugging assistance...")
    
    agent = SuperiorAIAgent()
    
    # Test error scenarios
    test_cases = [
        {
            "code": "print(undefined_variable)",
            "error": "NameError: name 'undefined_variable' is not defined",
            "language": "python"
        },
        {
            "code": "items = [1, 2, 3]\nprint(items[5])",
            "error": "IndexError: list index out of range",
            "language": "python"
        },
        {
            "code": "function test() { console.log(x); }",
            "error": "ReferenceError: x is not defined",
            "language": "javascript"
        },
        {
            "code": "while True:\n    print('infinite')",
            "error": "Performance issue: infinite loop detected",
            "language": "python"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        insights = agent.provide_debug_insights(
            case["code"], case["error"], case["language"]
        )
        
        print(f"✅ Error {i}: {len(insights)} insights")
        for insight in insights[:1]:  # Show top insight
            print(f"   - {insight.issue_type}: {insight.description}")
    
    return True


def test_architecture_analysis():
    """Test architecture analysis."""
    print("\n🧪 Testing architecture analysis...")
    
    agent = SuperiorAIAgent()
    
    # Test with current project
    analysis = agent.understand_code_architecture(".")
    
    print(f"✅ Architecture Type: {analysis['architecture_type']}")
    print(f"✅ Structure Quality: {analysis['structure_quality']}")
    print(f"✅ Patterns Detected: {len(analysis['patterns_detected'])}")
    print(f"✅ Recommendations: {len(analysis['recommendations'])}")
    
    if analysis['patterns_detected']:
        print(f"   Patterns: {', '.join(analysis['patterns_detected'])}")
    
    return True


def test_performance_metrics():
    """Test performance metrics tracking."""
    print("\n🧪 Testing performance metrics...")
    
    agent = SuperiorAIAgent()
    
    # Simulate some usage
    agent.analyze_code_quality("def test(): pass", "python")
    agent.generate_intelligent_completion("def ", 4, "python")
    agent.suggest_refactoring("var x = 1;", "javascript")
    
    # Get metrics
    metrics = agent.get_performance_metrics()
    
    print(f"✅ Suggestions Generated: {metrics['suggestions_generated']}")
    print(f"✅ Completions Generated: {metrics['completions_generated']}")
    print(f"✅ Debug Insights: {metrics['debug_insights_provided']}")
    print(f"✅ Efficiency Score: {metrics['efficiency_score']:.1%}")
    
    return True


def test_context_awareness():
    """Test context awareness capabilities."""
    print("\n🧪 Testing context awareness...")
    
    agent = SuperiorAIAgent()
    
    # Test context tracking
    agent.current_context["language"] = "python"
    agent.current_context["project_type"] = "web_application"
    
    # Test completion with context
    completions = agent.generate_intelligent_completion(
        "from flask import ", 12, "python", "app.py"
    )
    
    print(f"✅ Context-aware completions: {len(completions)}")
    
    # Test quality analysis with context
    web_code = '''
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/api/users', methods=['GET'])
def get_users():
    return jsonify({"users": []})
'''
    
    analysis = agent.analyze_code_quality(web_code, "python", "app.py")
    print(f"✅ Web app analysis quality: {analysis['quality_score']:.1%}")
    
    return True


def main():
    """Run all superior AI agent tests."""
    print("🚀 Superior AI Agent Test Suite")
    print("=" * 50)
    
    try:
        # Run tests
        test_code_quality_analysis()
        test_intelligent_completion()
        test_refactoring_suggestions()
        test_debugging_assistance()
        test_architecture_analysis()
        test_performance_metrics()
        test_context_awareness()
        
        print("\n" + "=" * 50)
        print("✅ All superior AI agent tests passed!")
        print("🤖 Superior AI capabilities are working correctly")
        print("🎯 Ready to surpass competing coding assistants!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
