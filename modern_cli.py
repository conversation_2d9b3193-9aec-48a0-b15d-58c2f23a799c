#!/usr/bin/env python3
"""
Modern CLI for Enhanced AI Coding Agent.

Provides a beautiful, modern command-line interface with all enhanced capabilities
integrated and enabled by default.
"""

import os
import sys
import click
import asyncio
from typing import Optional, Dict, Any
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import all components
from ui.terminal_interface import ModernTerminalInterface, run_interactive_mode
from optimization.large_codebase_optimizer import create_large_codebase_optimizer
from superior_ai.advanced_code_intelligence import create_advanced_code_intelligence
from enhanced_agent_orchestrator import EnhancedAgentOrchestrator
from tools.memory_tool import MemoryToolSpec
from config import get_config

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False


class ModernEnhancedAgent:
    """
    Modern Enhanced AI Coding Agent with all superior capabilities.
    
    Features:
    - Modern terminal interface (like opencode)
    - Enhanced mode enabled by default
    - Large codebase optimization
    - Superior AI capabilities
    - Advanced code intelligence
    - Memory system integration
    - Real-time chat interface
    - File explorer and code editor
    """
    
    def __init__(self, workspace_path: Optional[str] = None):
        """Initialize the modern enhanced agent."""
        self.workspace_path = workspace_path or os.getcwd()
        self.console = Console() if RICH_AVAILABLE else None
        
        # Initialize components
        self.terminal_interface = None
        self.codebase_optimizer = None
        self.code_intelligence = None
        self.orchestrator = None
        self.memory_tool = None
        
        # Configuration
        self.config = get_config()
        
        # Statistics
        self.stats = {
            'sessions': 0,
            'files_analyzed': 0,
            'suggestions_generated': 0,
            'issues_detected': 0
        }
    
    def initialize(self):
        """Initialize all components."""
        if self.console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Initializing Enhanced AI Agent...", total=5)
                
                # Initialize terminal interface
                progress.update(task, description="Setting up modern terminal interface...")
                self.terminal_interface = ModernTerminalInterface(self.workspace_path)
                progress.advance(task)
                
                # Initialize codebase optimizer
                progress.update(task, description="Initializing large codebase optimizer...")
                self.codebase_optimizer = create_large_codebase_optimizer(self.workspace_path)
                progress.advance(task)
                
                # Initialize code intelligence
                progress.update(task, description="Loading advanced code intelligence...")
                self.code_intelligence = create_advanced_code_intelligence(self.workspace_path)
                progress.advance(task)
                
                # Initialize orchestrator
                progress.update(task, description="Starting enhanced agent orchestrator...")
                self.orchestrator = EnhancedAgentOrchestrator()
                progress.advance(task)
                
                # Initialize memory system
                progress.update(task, description="Connecting memory system...")
                self.memory_tool = MemoryToolSpec()
                progress.advance(task)
        else:
            print("Initializing Enhanced AI Agent...")
            self.terminal_interface = ModernTerminalInterface(self.workspace_path)
            self.codebase_optimizer = create_large_codebase_optimizer(self.workspace_path)
            self.code_intelligence = create_advanced_code_intelligence(self.workspace_path)
            self.orchestrator = EnhancedAgentOrchestrator()
            self.memory_tool = MemoryToolSpec()
    
    def start_interactive_mode(self):
        """Start the interactive terminal mode."""
        self._show_welcome_message()
        
        if self.terminal_interface:
            self.terminal_interface.start()
        else:
            self._fallback_interactive_mode()
    
    def analyze_codebase(self, force_rescan: bool = False):
        """Analyze the entire codebase."""
        if self.console:
            self.console.print("🔍 [bold blue]Analyzing Codebase[/bold blue]")
        else:
            print("🔍 Analyzing Codebase")
        
        if self.codebase_optimizer:
            stats = self.codebase_optimizer.scan_codebase(force_rescan)
            self._display_analysis_results(stats)
        else:
            print("❌ Codebase optimizer not available")
    
    def analyze_file(self, file_path: str):
        """Analyze a specific file."""
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return
        
        if self.console:
            self.console.print(f"🔍 [bold blue]Analyzing File:[/bold blue] {file_path}")
        else:
            print(f"🔍 Analyzing File: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            if self.code_intelligence:
                analysis = self.code_intelligence.analyze_code(file_path, content)
                self._display_file_analysis(analysis)
            else:
                print("❌ Code intelligence not available")
        
        except Exception as e:
            print(f"❌ Error analyzing file: {e}")
    
    def get_code_suggestions(self, file_path: str, line: int, column: int):
        """Get code suggestions for a specific position."""
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            if self.code_intelligence:
                suggestions = self.code_intelligence.generate_code_completion(
                    file_path, content, line - 1, column
                )
                self._display_suggestions(suggestions)
            else:
                print("❌ Code intelligence not available")
        
        except Exception as e:
            print(f"❌ Error getting suggestions: {e}")
    
    def get_refactoring_suggestions(self, file_path: str):
        """Get refactoring suggestions for a file."""
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            if self.code_intelligence:
                opportunities = self.code_intelligence.suggest_refactoring(file_path, content)
                self._display_refactoring_opportunities(opportunities)
            else:
                print("❌ Code intelligence not available")
        
        except Exception as e:
            print(f"❌ Error getting refactoring suggestions: {e}")
    
    def analyze_architecture(self):
        """Analyze project architecture."""
        if self.console:
            self.console.print("🏗️ [bold blue]Analyzing Project Architecture[/bold blue]")
        else:
            print("🏗️ Analyzing Project Architecture")
        
        if self.code_intelligence:
            analysis = self.code_intelligence.analyze_project_architecture()
            self._display_architecture_analysis(analysis)
        else:
            print("❌ Code intelligence not available")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status of the agent."""
        status = {
            'workspace': self.workspace_path,
            'components': {
                'terminal_interface': self.terminal_interface is not None,
                'codebase_optimizer': self.codebase_optimizer is not None,
                'code_intelligence': self.code_intelligence is not None,
                'orchestrator': self.orchestrator is not None,
                'memory_tool': self.memory_tool is not None
            },
            'stats': self.stats,
            'rich_available': RICH_AVAILABLE
        }
        
        if self.codebase_optimizer:
            status['codebase_stats'] = self.codebase_optimizer.get_statistics()
        
        return status
    
    def _show_welcome_message(self):
        """Show welcome message."""
        if self.console:
            welcome_text = Text()
            welcome_text.append("🚀 ", style="bold blue")
            welcome_text.append("Enhanced AI Coding Agent", style="bold green")
            welcome_text.append(" - Modern Terminal Interface\n\n", style="bold blue")
            welcome_text.append("Features Enabled:\n", style="bold yellow")
            welcome_text.append("✅ Modern Terminal UI (opencode-style)\n", style="green")
            welcome_text.append("✅ Enhanced Mode (default)\n", style="green")
            welcome_text.append("✅ Large Codebase Optimization\n", style="green")
            welcome_text.append("✅ Superior AI Capabilities\n", style="green")
            welcome_text.append("✅ Advanced Code Intelligence\n", style="green")
            welcome_text.append("✅ Memory System Integration\n", style="green")
            welcome_text.append("✅ Real-time Chat Interface\n", style="green")
            welcome_text.append("✅ File Explorer & Code Editor\n", style="green")
            welcome_text.append("\nWorkspace: ", style="dim")
            welcome_text.append(f"{self.workspace_path}\n", style="cyan")
            
            panel = Panel(
                welcome_text,
                title="🎉 Welcome to Enhanced AI Coding Agent",
                border_style="blue",
                padding=(1, 2)
            )
            
            self.console.print(panel)
        else:
            print("🚀 Enhanced AI Coding Agent - Modern Terminal Interface")
            print("=" * 60)
            print("Features Enabled:")
            print("✅ Modern Terminal UI (opencode-style)")
            print("✅ Enhanced Mode (default)")
            print("✅ Large Codebase Optimization")
            print("✅ Superior AI Capabilities")
            print("✅ Advanced Code Intelligence")
            print("✅ Memory System Integration")
            print("✅ Real-time Chat Interface")
            print("✅ File Explorer & Code Editor")
            print(f"\nWorkspace: {self.workspace_path}")
            print("=" * 60)
    
    def _display_analysis_results(self, stats: Dict[str, Any]):
        """Display codebase analysis results."""
        if self.console:
            table = Table(title="📊 Codebase Analysis Results")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            for key, value in stats.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        table.add_row(f"{key}.{sub_key}", str(sub_value))
                else:
                    table.add_row(key, str(value))
            
            self.console.print(table)
        else:
            print("📊 Codebase Analysis Results:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
    
    def _display_file_analysis(self, analysis: Dict[str, Any]):
        """Display file analysis results."""
        if self.console:
            self.console.print(f"📄 [bold]File:[/bold] {analysis.get('file_path', 'Unknown')}")
            self.console.print(f"🔤 [bold]Language:[/bold] {analysis.get('language', 'Unknown')}")
            self.console.print(f"🔢 [bold]Complexity:[/bold] {analysis.get('complexity', 0)}")
            
            suggestions = analysis.get('suggestions', [])
            if suggestions:
                self.console.print(f"\n💡 [bold yellow]{len(suggestions)} Suggestions:[/bold yellow]")
                for i, suggestion in enumerate(suggestions[:5], 1):
                    self.console.print(f"  {i}. {suggestion.description}")
            
            issues = analysis.get('issues', [])
            if issues:
                self.console.print(f"\n⚠️ [bold red]{len(issues)} Issues:[/bold red]")
                for i, issue in enumerate(issues[:5], 1):
                    self.console.print(f"  {i}. {issue.message}")
        else:
            print(f"📄 File: {analysis.get('file_path', 'Unknown')}")
            print(f"🔤 Language: {analysis.get('language', 'Unknown')}")
            print(f"🔢 Complexity: {analysis.get('complexity', 0)}")
            
            suggestions = analysis.get('suggestions', [])
            if suggestions:
                print(f"\n💡 {len(suggestions)} Suggestions:")
                for i, suggestion in enumerate(suggestions[:5], 1):
                    print(f"  {i}. {suggestion.description}")
            
            issues = analysis.get('issues', [])
            if issues:
                print(f"\n⚠️ {len(issues)} Issues:")
                for i, issue in enumerate(issues[:5], 1):
                    print(f"  {i}. {issue.message}")
    
    def _display_suggestions(self, suggestions):
        """Display code suggestions."""
        if not suggestions:
            print("💡 No suggestions available")
            return
        
        if self.console:
            self.console.print(f"💡 [bold yellow]{len(suggestions)} Code Suggestions:[/bold yellow]")
            for i, suggestion in enumerate(suggestions, 1):
                self.console.print(f"  {i}. [bold]{suggestion.description}[/bold]")
                self.console.print(f"     Code: [cyan]{suggestion.code}[/cyan]")
                self.console.print(f"     Confidence: {suggestion.confidence:.1%}")
        else:
            print(f"💡 {len(suggestions)} Code Suggestions:")
            for i, suggestion in enumerate(suggestions, 1):
                print(f"  {i}. {suggestion.description}")
                print(f"     Code: {suggestion.code}")
                print(f"     Confidence: {suggestion.confidence:.1%}")
    
    def _display_refactoring_opportunities(self, opportunities):
        """Display refactoring opportunities."""
        if not opportunities:
            print("🔧 No refactoring opportunities found")
            return
        
        if self.console:
            self.console.print(f"🔧 [bold yellow]{len(opportunities)} Refactoring Opportunities:[/bold yellow]")
            for i, opp in enumerate(opportunities, 1):
                self.console.print(f"  {i}. [bold]{opp.description}[/bold]")
                self.console.print(f"     Type: {opp.type}")
                self.console.print(f"     Benefit: {opp.benefit}")
                self.console.print(f"     Effort: {opp.effort}")
        else:
            print(f"🔧 {len(opportunities)} Refactoring Opportunities:")
            for i, opp in enumerate(opportunities, 1):
                print(f"  {i}. {opp.description}")
                print(f"     Type: {opp.type}")
                print(f"     Benefit: {opp.benefit}")
                print(f"     Effort: {opp.effort}")
    
    def _display_architecture_analysis(self, analysis):
        """Display architecture analysis."""
        if self.console:
            self.console.print("🏗️ [bold blue]Architecture Analysis:[/bold blue]")
            
            metrics = analysis.get('metrics', {})
            if metrics:
                self.console.print(f"📊 Files analyzed: {metrics.get('files_analyzed', 0)}")
            
            recommendations = analysis.get('recommendations', [])
            if recommendations:
                self.console.print(f"\n💡 [bold yellow]Recommendations:[/bold yellow]")
                for i, rec in enumerate(recommendations, 1):
                    self.console.print(f"  {i}. {rec}")
        else:
            print("🏗️ Architecture Analysis:")
            
            metrics = analysis.get('metrics', {})
            if metrics:
                print(f"📊 Files analyzed: {metrics.get('files_analyzed', 0)}")
            
            recommendations = analysis.get('recommendations', [])
            if recommendations:
                print(f"\n💡 Recommendations:")
                for i, rec in enumerate(recommendations, 1):
                    print(f"  {i}. {rec}")
    
    def _fallback_interactive_mode(self):
        """Fallback interactive mode when rich UI is not available."""
        print("\nEntering interactive mode (fallback)...")
        print("Type 'help' for commands, 'exit' to quit")
        
        while True:
            try:
                user_input = input("\n💬 You: ").strip()
                
                if user_input.lower() in ['exit', 'quit', 'q']:
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                elif user_input.lower() == 'status':
                    status = self.get_status()
                    print(f"Status: {status}")
                else:
                    print("🤖 AI: Processing your request...")
                    # This would integrate with the enhanced agent
                    print(f"Response to: {user_input}")
            
            except KeyboardInterrupt:
                break
        
        print("\nGoodbye! 👋")
    
    def _show_help(self):
        """Show help information."""
        help_text = """
🚀 Enhanced AI Coding Agent - Commands:

General:
  help     - Show this help message
  status   - Show agent status
  exit     - Exit the application

Analysis:
  analyze  - Analyze entire codebase
  file <path> - Analyze specific file
  suggest <path> <line> <col> - Get code suggestions
  refactor <path> - Get refactoring suggestions
  arch     - Analyze project architecture

Interactive:
  chat     - Start chat mode
  ui       - Start modern terminal UI

Examples:
  file src/main.py
  suggest src/main.py 10 5
  refactor src/utils.py
"""
        print(help_text)


@click.group()
@click.option('--workspace', '-w', default=None, help='Workspace directory')
@click.pass_context
def cli(ctx, workspace):
    """🚀 Enhanced AI Coding Agent - Modern CLI with Superior Capabilities"""
    ctx.ensure_object(dict)
    ctx.obj['workspace'] = workspace or os.getcwd()


@cli.command()
@click.pass_context
def interactive(ctx):
    """Start interactive terminal mode with modern UI"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    agent.start_interactive_mode()


@cli.command()
@click.option('--force', '-f', is_flag=True, help='Force rescan of all files')
@click.pass_context
def analyze(ctx, force):
    """Analyze the entire codebase"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    agent.analyze_codebase(force)


@cli.command()
@click.argument('file_path')
@click.pass_context
def file(ctx, file_path):
    """Analyze a specific file"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    agent.analyze_file(file_path)


@cli.command()
@click.argument('file_path')
@click.argument('line', type=int)
@click.argument('column', type=int)
@click.pass_context
def suggest(ctx, file_path, line, column):
    """Get code suggestions for a specific position"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    agent.get_code_suggestions(file_path, line, column)


@cli.command()
@click.argument('file_path')
@click.pass_context
def refactor(ctx, file_path):
    """Get refactoring suggestions for a file"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    agent.get_refactoring_suggestions(file_path)


@cli.command()
@click.pass_context
def arch(ctx):
    """Analyze project architecture"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    agent.analyze_architecture()


@cli.command()
@click.pass_context
def status(ctx):
    """Show agent status and statistics"""
    agent = ModernEnhancedAgent(ctx.obj['workspace'])
    agent.initialize()
    status = agent.get_status()
    
    if RICH_AVAILABLE:
        console = Console()
        table = Table(title="🤖 Enhanced AI Agent Status")
        table.add_column("Component", style="cyan")
        table.add_column("Status", style="green")
        
        for component, enabled in status['components'].items():
            status_text = "✅ Enabled" if enabled else "❌ Disabled"
            table.add_row(component.replace('_', ' ').title(), status_text)
        
        console.print(table)
        console.print(f"\n📁 Workspace: {status['workspace']}")
    else:
        print("🤖 Enhanced AI Agent Status:")
        for component, enabled in status['components'].items():
            status_text = "✅ Enabled" if enabled else "❌ Disabled"
            print(f"  {component.replace('_', ' ').title()}: {status_text}")
        print(f"\n📁 Workspace: {status['workspace']}")


@cli.command()
@click.pass_context
def ui(ctx):
    """Start modern terminal UI (opencode-style)"""
    run_interactive_mode(ctx.obj['workspace'])


if __name__ == '__main__':
    # Default to interactive mode if no command specified
    if len(sys.argv) == 1:
        agent = ModernEnhancedAgent()
        agent.initialize()
        agent.start_interactive_mode()
    else:
        cli()
