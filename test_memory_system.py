#!/usr/bin/env python3
"""
Test script for the Enhanced Memory System.

This script tests the comprehensive memory capabilities including:
- Memory storage and retrieval
- Different memory scopes and priorities
- Cross-session persistence
- Memory search and filtering
"""

import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from memory_system import (
    get_memory_system, MemoryScope, MemoryPriority,
    remember, recall
)


def test_basic_memory_operations():
    """Test basic memory storage and retrieval."""
    print("🧪 Testing basic memory operations...")
    
    # Get memory system
    memory_system = get_memory_system()
    
    # Store some memories
    memory_id1 = memory_system.store_memory(
        content="Python is a high-level programming language",
        memory_type="knowledge",
        scope=MemoryScope.GLOBAL,
        priority=MemoryPriority.HIGH,
        importance_score=0.8,
        tags=["python", "programming", "language"]
    )
    
    memory_id2 = memory_system.store_memory(
        content="Use 'git commit -m' to commit changes with a message",
        memory_type="command",
        scope=MemoryScope.PROJECT,
        priority=MemoryPriority.MEDIUM,
        importance_score=0.7,
        tags=["git", "command", "version-control"]
    )
    
    memory_id3 = memory_system.store_memory(
        content="The user prefers dark theme for the terminal",
        memory_type="user_preference",
        scope=MemoryScope.USER,
        priority=MemoryPriority.MEDIUM,
        importance_score=0.6,
        tags=["preference", "ui", "theme"]
    )
    
    print(f"✅ Stored 3 memories: {memory_id1[:8]}..., {memory_id2[:8]}..., {memory_id3[:8]}...")
    
    # Test retrieval
    python_memories = memory_system.retrieve_memories(query="python", limit=5)
    print(f"✅ Found {len(python_memories)} memories about Python")
    
    git_memories = memory_system.retrieve_memories(query="git", limit=5)
    print(f"✅ Found {len(git_memories)} memories about Git")
    
    # Test scope filtering
    global_memories = memory_system.retrieve_memories(scope=MemoryScope.GLOBAL, limit=10)
    print(f"✅ Found {len(global_memories)} global memories")
    
    user_memories = memory_system.retrieve_memories(scope=MemoryScope.USER, limit=10)
    print(f"✅ Found {len(user_memories)} user preference memories")
    
    return True


def test_convenience_functions():
    """Test convenience functions for memory operations."""
    print("\n🧪 Testing convenience functions...")
    
    # Test remember function
    memory_id = remember(
        "FastAPI is a modern web framework for Python",
        memory_type="knowledge",
        scope=MemoryScope.GLOBAL,
        tags=["fastapi", "python", "web", "framework"]
    )
    print(f"✅ Remembered FastAPI knowledge: {memory_id[:8]}...")
    
    # Test recall function
    fastapi_memories = recall("FastAPI")
    print(f"✅ Recalled {len(fastapi_memories)} memories about FastAPI")
    
    web_memories = recall("web framework")
    print(f"✅ Recalled {len(web_memories)} memories about web frameworks")
    
    return True


def test_memory_stats():
    """Test memory statistics and management."""
    print("\n🧪 Testing memory statistics...")
    
    memory_system = get_memory_system()
    stats = memory_system.get_memory_stats()
    
    print(f"✅ Memory Statistics:")
    print(f"   - Session memories: {stats['session_memories']}")
    print(f"   - Conversation memories: {stats['conversation_memories']}")
    print(f"   - Persistent memories: {stats['persistent_memories']}")
    print(f"   - Memories created: {stats['memories_created']}")
    print(f"   - Memories accessed: {stats['memories_accessed']}")
    print(f"   - Current session: {stats['current_session_id']}")
    
    return True


def test_session_and_conversation_memory():
    """Test session and conversation scoped memory."""
    print("\n🧪 Testing session and conversation memory...")
    
    memory_system = get_memory_system()
    
    # Set conversation context
    memory_system.set_conversation_context("test_conversation_123")
    
    # Store session memory
    session_id = memory_system.store_memory(
        content="This is a temporary note for this session",
        memory_type="note",
        scope=MemoryScope.SESSION,
        priority=MemoryPriority.TEMPORARY
    )
    
    # Store conversation memory
    conv_id = memory_system.store_memory(
        content="User asked about Python best practices",
        memory_type="context",
        scope=MemoryScope.CONVERSATION,
        priority=MemoryPriority.MEDIUM
    )
    
    print(f"✅ Stored session memory: {session_id[:8]}...")
    print(f"✅ Stored conversation memory: {conv_id[:8]}...")
    
    # Retrieve session memories
    session_memories = memory_system.retrieve_memories(scope=MemoryScope.SESSION)
    print(f"✅ Found {len(session_memories)} session memories")
    
    # Retrieve conversation memories
    conv_memories = memory_system.retrieve_memories(scope=MemoryScope.CONVERSATION)
    print(f"✅ Found {len(conv_memories)} conversation memories")
    
    return True


def test_memory_search():
    """Test advanced memory search capabilities."""
    print("\n🧪 Testing memory search capabilities...")
    
    memory_system = get_memory_system()
    
    # Store some test memories with different content
    memory_system.store_memory(
        content="To debug Python code, use print statements or the debugger",
        memory_type="tip",
        tags=["python", "debugging", "development"]
    )
    
    memory_system.store_memory(
        content="JavaScript async/await makes asynchronous code easier to read",
        memory_type="tip",
        tags=["javascript", "async", "programming"]
    )
    
    # Test different search queries
    debug_memories = memory_system.retrieve_memories(query="debug", limit=5)
    print(f"✅ Found {len(debug_memories)} memories about debugging")
    
    async_memories = memory_system.retrieve_memories(query="async", limit=5)
    print(f"✅ Found {len(async_memories)} memories about async programming")
    
    tip_memories = memory_system.retrieve_memories(memory_type="tip", limit=10)
    print(f"✅ Found {len(tip_memories)} tip-type memories")
    
    return True


def main():
    """Run all memory system tests."""
    print("🚀 Enhanced Memory System Test Suite")
    print("=" * 50)
    
    try:
        # Run tests
        test_basic_memory_operations()
        test_convenience_functions()
        test_session_and_conversation_memory()
        test_memory_search()
        test_memory_stats()
        
        print("\n" + "=" * 50)
        print("✅ All memory system tests passed!")
        print("🧠 Enhanced Memory System is working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
