#!/usr/bin/env python3
"""
Basic functionality test for the enhanced AI agent.
This script tests that all imports work and basic functionality is available.
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_imports():
    """Test that basic imports work."""
    print("🧪 Testing basic imports...")
    
    try:
        from cli import main
        print("✅ CLI import successful")
    except Exception as e:
        print(f"❌ CLI import failed: {e}")
        return False
    
    try:
        from chat import chat, create_initial_messages
        print("✅ Chat import successful")
    except Exception as e:
        print(f"❌ Chat import failed: {e}")
        return False
    
    try:
        from message import Message, create_user_message, create_system_message
        print("✅ Message import successful")
    except Exception as e:
        print(f"❌ Message import failed: {e}")
        return False
    
    try:
        from config import get_config
        print("✅ Config import successful")
    except Exception as e:
        print(f"❌ Config import failed: {e}")
        return False
    
    return True

def test_enhanced_imports():
    """Test that enhanced imports work."""
    print("\n🚀 Testing enhanced imports...")
    
    try:
        from enhanced_agent_orchestrator import get_enhanced_orchestrator
        orchestrator = get_enhanced_orchestrator()
        print("✅ Enhanced orchestrator import successful")
        return True
    except Exception as e:
        print(f"⚠️ Enhanced orchestrator import failed: {e}")
        return False

def test_enhanced_tools():
    """Test enhanced tools imports."""
    print("\n🛠️ Testing enhanced tools...")
    
    tools_status = {}
    
    try:
        from tools.enhanced_tool_discovery import get_enhanced_discovery
        discovery = get_enhanced_discovery()
        tools_status['tool_discovery'] = True
        print("✅ Enhanced tool discovery available")
    except Exception as e:
        tools_status['tool_discovery'] = False
        print(f"⚠️ Enhanced tool discovery failed: {e}")
    
    try:
        from tools.sequential_thinking import SequentialThinkingTool
        thinking_tool = SequentialThinkingTool()
        tools_status['sequential_thinking'] = True
        print("✅ Sequential thinking tool available")
    except Exception as e:
        tools_status['sequential_thinking'] = False
        print(f"⚠️ Sequential thinking tool failed: {e}")
    
    try:
        from tools.enhanced_research import EnhancedResearchTool
        research_tool = EnhancedResearchTool()
        tools_status['enhanced_research'] = True
        print("✅ Enhanced research tool available")
    except Exception as e:
        tools_status['enhanced_research'] = False
        print(f"⚠️ Enhanced research tool failed: {e}")
    
    try:
        from tools.advanced_codebase_analyzer import AdvancedCodebaseAnalyzer
        analyzer = AdvancedCodebaseAnalyzer()
        tools_status['codebase_analyzer'] = True
        print("✅ Advanced codebase analyzer available")
    except Exception as e:
        tools_status['codebase_analyzer'] = False
        print(f"⚠️ Advanced codebase analyzer failed: {e}")
    
    return tools_status

def test_cli_execution():
    """Test CLI execution without errors."""
    print("\n💻 Testing CLI execution...")
    
    try:
        # Test help command
        import subprocess
        result = subprocess.run([
            sys.executable, "cli.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ CLI help command works")
            return True
        else:
            print(f"❌ CLI help command failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ CLI execution test failed: {e}")
        return False

def test_basic_chat_functionality():
    """Test basic chat functionality."""
    print("\n💬 Testing basic chat functionality...")
    
    try:
        from chat import create_initial_messages
        from message import create_user_message
        
        # Create initial messages
        messages = create_initial_messages()
        print(f"✅ Created {len(messages)} initial messages")
        
        # Create user message
        user_msg = create_user_message("Hello, this is a test")
        print(f"✅ Created user message: {user_msg.role}")
        
        return True
    except Exception as e:
        print(f"❌ Basic chat functionality failed: {e}")
        return False

def test_enhanced_orchestrator_functionality():
    """Test enhanced orchestrator basic functionality."""
    print("\n🤖 Testing enhanced orchestrator functionality...")
    
    try:
        from enhanced_agent_orchestrator import get_enhanced_orchestrator, TaskComplexity, AgentMode
        
        orchestrator = get_enhanced_orchestrator()
        print("✅ Orchestrator created successfully")
        
        # Test task complexity analysis
        orchestrator.active_context.task_complexity = TaskComplexity.MODERATE
        messages = list(orchestrator._analyze_task_complexity("What is Python?"))
        print(f"✅ Task complexity analysis generated {len(messages)} messages")
        
        # Test agent mode determination
        orchestrator.active_context.agent_mode = AgentMode.INTERACTIVE
        messages = list(orchestrator._determine_agent_mode("Create a Python script"))
        print(f"✅ Agent mode determination generated {len(messages)} messages")
        
        # Test performance summary
        summary = orchestrator.get_performance_summary()
        print(f"✅ Performance summary generated with {len(summary)} sections")
        
        return True
    except Exception as e:
        print(f"❌ Enhanced orchestrator functionality failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 ENHANCED AI AGENT - BASIC FUNCTIONALITY TEST")
    print("=" * 60)
    
    test_results = {}
    
    # Run tests
    test_results['basic_imports'] = test_basic_imports()
    test_results['enhanced_imports'] = test_enhanced_imports()
    test_results['enhanced_tools'] = test_enhanced_tools()
    test_results['cli_execution'] = test_cli_execution()
    test_results['basic_chat'] = test_basic_chat_functionality()
    test_results['enhanced_orchestrator'] = test_enhanced_orchestrator_functionality()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = 0
    
    for test_name, result in test_results.items():
        if isinstance(result, dict):
            # For enhanced tools, count individual tools
            for tool_name, tool_result in result.items():
                total += 1
                if tool_result:
                    passed += 1
                    print(f"✅ {tool_name}: PASSED")
                else:
                    print(f"⚠️ {tool_name}: FAILED (non-critical)")
        else:
            total += 1
            if result:
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
    
    print(f"\n📈 Results: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if test_results['basic_imports'] and test_results['basic_chat']:
        print("\n🎉 CORE FUNCTIONALITY: WORKING")
        print("The basic AI agent is functional and ready to use.")
        
        if test_results['enhanced_imports'] and test_results['enhanced_orchestrator']:
            print("🚀 ENHANCED FUNCTIONALITY: WORKING")
            print("The enhanced AI agent is fully functional with advanced capabilities.")
        else:
            print("⚠️ ENHANCED FUNCTIONALITY: PARTIAL")
            print("Enhanced features may have limited functionality but core agent works.")
    else:
        print("\n❌ CORE FUNCTIONALITY: FAILED")
        print("Basic functionality needs to be fixed before proceeding.")
    
    print("\n" + "=" * 60)
    
    # Return success if core functionality works
    return test_results['basic_imports'] and test_results['basic_chat']

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
