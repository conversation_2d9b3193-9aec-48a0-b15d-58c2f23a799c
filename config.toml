# Enhanced AI Coding Agent Configuration
# This file contains the default configuration for the Enhanced AI Coding Agent

[llm]
# Language model configuration
model = "gemini-2.0-flash-exp"
temperature = 0.7
max_tokens = 8192
top_p = 0.95
top_k = 40
timeout = 60
max_retries = 3

[tools]
# Tool configuration
enabled = [
    "shell", "code", "file", "web", "codebase",
    "browser", "computer", "github", "rag", "memory", "process"
]
auto_confirm = false
timeout = 300
max_output_length = 10000

[server]
# Web server configuration
host = "127.0.0.1"
port = 5000
debug = false
cors_enabled = true
max_content_length = 16777216  # 16MB

[features]
# Enhanced features configuration
enhanced_mode = true  # Enhanced mode enabled by default
telemetry_enabled = false
auto_commit = false
cost_tracking = true
conversation_backup = true
pre_commit_checks = true

# Enhanced mode features
[enhanced]
# Tool discovery and recommendations
tool_discovery_enabled = true
max_tool_recommendations = 5
tool_confidence_threshold = 0.7

# Sequential thinking (Chain of Thought)
sequential_thinking_enabled = true
max_thinking_iterations = 10
thinking_types = ["chain_of_thought", "self_critique", "planning"]

# Research capabilities
research_enabled = true
max_research_sources = 10
research_cache_ttl = "24h"

# Advanced codebase analysis
codebase_analysis_enabled = true
max_analysis_depth = 3
include_dependencies = true
include_patterns = true

# Memory and context
memory_enabled = true
conversation_memory_limit = 1000
project_memory_enabled = true
context_retention_hours = 24

# Performance optimizations
parallel_processing = true
caching_enabled = true
incremental_analysis = true
large_codebase_optimization = true

# UI preferences
[ui]
theme = "dark"
layout = "split"
syntax_highlighting = true
file_tree_visible = true
terminal_visible = true
chat_history_limit = 1000
font_size = 14
font_family = "JetBrains Mono"
auto_save = true

# Modern terminal interface
[ui.modern_terminal]
enabled = true
real_time_chat = true
code_editor_integration = true
file_explorer_integration = true
terminal_integration = true

# Syntax highlighting
[ui.syntax_highlighting]
enabled = true
theme = "auto"  # auto, dark, light, claude, github_dark, github_light, monokai
line_numbers = true
word_wrap = false
supported_languages = [
    "python", "javascript", "typescript", "java", "c", "c++", "rust", "go",
    "html", "css", "json", "yaml", "markdown", "bash", "sql", "xml"
]
