"""
Advanced Syntax Highlighter for Enhanced AI Coding Agent.

This module provides comprehensive syntax highlighting for multiple
programming languages with modern themes and styling.
"""

import logging
from typing import Optional, Dict, Any, List, Union
from enum import Enum

try:
    from rich.console import Console
    from rich.syntax import Syntax
    from rich.text import Text
    from rich.style import Style
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

try:
    import pygments
    from pygments import highlight
    from pygments.lexers import get_lexer_by_name, guess_lexer
    from pygments.formatters import TerminalFormatter, Terminal256Formatter
    from pygments.styles import get_style_by_name
    PYGMENTS_AVAILABLE = True
except ImportError:
    PYGMENTS_AVAILABLE = False

logger = logging.getLogger(__name__)


class HighlightTheme(Enum):
    """Syntax highlighting themes."""
    DARK = "dark"
    LIGHT = "light"
    CLAUDE = "claude"
    GITHUB_DARK = "github_dark"
    GITHUB_LIGHT = "github_light"
    MONOKAI = "monokai"
    SOLARIZED_DARK = "solarized_dark"
    SOLARIZED_LIGHT = "solarized_light"


class SyntaxHighlighter:
    """
    Advanced syntax highlighter with multiple language support.
    
    Features:
    - Support for 100+ programming languages
    - Multiple themes (dark, light, claude-inspired)
    - Rich terminal output with colors
    - Fallback to simple highlighting
    - Code block detection and highlighting
    - Custom theme support
    """
    
    def __init__(
        self,
        theme: str = "dark",
        line_numbers: bool = False,
        word_wrap: bool = False
    ):
        """
        Initialize the syntax highlighter.
        
        Args:
            theme: Highlighting theme
            line_numbers: Whether to show line numbers
            word_wrap: Whether to wrap long lines
        """
        self.theme = HighlightTheme(theme) if isinstance(theme, str) else theme
        self.line_numbers = line_numbers
        self.word_wrap = word_wrap
        
        self.console = Console() if RICH_AVAILABLE else None
        
        # Language aliases
        self.language_aliases = {
            'py': 'python',
            'js': 'javascript',
            'ts': 'typescript',
            'tsx': 'typescript',
            'jsx': 'javascript',
            'sh': 'bash',
            'yml': 'yaml',
            'md': 'markdown',
            'cpp': 'c++',
            'hpp': 'c++',
            'h': 'c',
            'cs': 'csharp',
            'rb': 'ruby',
            'php': 'php',
            'go': 'go',
            'rs': 'rust',
            'kt': 'kotlin',
            'swift': 'swift',
            'scala': 'scala',
            'r': 'r',
            'matlab': 'matlab',
            'sql': 'sql',
            'html': 'html',
            'css': 'css',
            'scss': 'scss',
            'sass': 'sass',
            'less': 'less',
            'xml': 'xml',
            'json': 'json',
            'toml': 'toml',
            'ini': 'ini',
            'cfg': 'ini',
            'conf': 'ini',
            'dockerfile': 'dockerfile',
            'makefile': 'makefile',
            'cmake': 'cmake',
            'powershell': 'powershell',
            'ps1': 'powershell',
            'batch': 'batch',
            'bat': 'batch'
        }
        
        # Theme mappings for Pygments
        self.pygments_themes = {
            HighlightTheme.DARK: "monokai",
            HighlightTheme.LIGHT: "default",
            HighlightTheme.CLAUDE: "github-light",
            HighlightTheme.GITHUB_DARK: "github-dark",
            HighlightTheme.GITHUB_LIGHT: "github-light",
            HighlightTheme.MONOKAI: "monokai",
            HighlightTheme.SOLARIZED_DARK: "solarized-dark",
            HighlightTheme.SOLARIZED_LIGHT: "solarized-light"
        }
        
        # Rich themes
        self.rich_themes = {
            HighlightTheme.DARK: "monokai",
            HighlightTheme.LIGHT: "github-light",
            HighlightTheme.CLAUDE: "github-light",
            HighlightTheme.GITHUB_DARK: "github-dark",
            HighlightTheme.GITHUB_LIGHT: "github-light",
            HighlightTheme.MONOKAI: "monokai",
            HighlightTheme.SOLARIZED_DARK: "solarized-dark",
            HighlightTheme.SOLARIZED_LIGHT: "solarized-light"
        }
        
        # Simple color schemes for fallback
        self._setup_fallback_colors()
    
    def _setup_fallback_colors(self) -> None:
        """Setup fallback color schemes."""
        if self.theme in [HighlightTheme.DARK, HighlightTheme.GITHUB_DARK, HighlightTheme.MONOKAI]:
            self.fallback_colors = {
                'keyword': '\033[95m',      # Magenta
                'string': '\033[92m',       # Green
                'comment': '\033[90m',      # Dark gray
                'number': '\033[94m',       # Blue
                'function': '\033[96m',     # Cyan
                'class': '\033[93m',        # Yellow
                'operator': '\033[91m',     # Red
                'reset': '\033[0m'          # Reset
            }
        else:
            self.fallback_colors = {
                'keyword': '\033[35m',      # Magenta
                'string': '\033[32m',       # Green
                'comment': '\033[37m',      # Light gray
                'number': '\033[34m',       # Blue
                'function': '\033[36m',     # Cyan
                'class': '\033[33m',        # Yellow
                'operator': '\033[31m',     # Red
                'reset': '\033[0m'          # Reset
            }
    
    def highlight_code(
        self,
        code: str,
        language: str,
        theme: Optional[str] = None
    ) -> Union[Syntax, Text, str]:
        """
        Highlight code with syntax highlighting.
        
        Args:
            code: Code to highlight
            language: Programming language
            theme: Override theme for this highlighting
        
        Returns:
            Highlighted code (Rich Syntax, Text, or string)
        """
        # Normalize language
        language = self._normalize_language(language)
        
        # Use override theme if provided
        current_theme = HighlightTheme(theme) if theme else self.theme
        
        # Try Rich syntax highlighting first
        if RICH_AVAILABLE:
            try:
                return self._highlight_with_rich(code, language, current_theme)
            except Exception as e:
                logger.debug(f"Rich highlighting failed: {e}")
        
        # Try Pygments highlighting
        if PYGMENTS_AVAILABLE:
            try:
                return self._highlight_with_pygments(code, language, current_theme)
            except Exception as e:
                logger.debug(f"Pygments highlighting failed: {e}")
        
        # Fallback to simple highlighting
        return self._highlight_fallback(code, language)
    
    def _normalize_language(self, language: str) -> str:
        """Normalize language name."""
        language = language.lower().strip()
        return self.language_aliases.get(language, language)
    
    def _highlight_with_rich(
        self,
        code: str,
        language: str,
        theme: HighlightTheme
    ) -> Syntax:
        """Highlight code using Rich."""
        rich_theme = self.rich_themes.get(theme, "monokai")
        
        return Syntax(
            code,
            language,
            theme=rich_theme,
            line_numbers=self.line_numbers,
            word_wrap=self.word_wrap,
            background_color="default"
        )
    
    def _highlight_with_pygments(
        self,
        code: str,
        language: str,
        theme: HighlightTheme
    ) -> str:
        """Highlight code using Pygments."""
        try:
            lexer = get_lexer_by_name(language)
        except:
            # Try to guess the lexer
            try:
                lexer = guess_lexer(code)
            except:
                # Use text lexer as fallback
                lexer = get_lexer_by_name('text')
        
        # Get theme
        pygments_theme = self.pygments_themes.get(theme, "monokai")
        
        # Create formatter
        formatter = Terminal256Formatter(style=pygments_theme)
        
        # Highlight code
        highlighted = highlight(code, lexer, formatter)
        
        return highlighted.rstrip('\n')
    
    def _highlight_fallback(self, code: str, language: str) -> str:
        """Simple fallback highlighting."""
        if language == 'text' or not code.strip():
            return code
        
        # Simple keyword highlighting for common languages
        keywords = self._get_language_keywords(language)
        
        lines = code.split('\n')
        highlighted_lines = []
        
        for line in lines:
            highlighted_line = line
            
            # Highlight comments
            if '//' in line or '#' in line:
                comment_start = line.find('//')
                if comment_start == -1:
                    comment_start = line.find('#')
                
                if comment_start >= 0:
                    before_comment = line[:comment_start]
                    comment = line[comment_start:]
                    highlighted_line = (before_comment + 
                                      self.fallback_colors['comment'] + 
                                      comment + 
                                      self.fallback_colors['reset'])
            
            # Highlight strings
            import re
            string_pattern = r'(["\'])(?:(?=(\\?))\2.)*?\1'
            highlighted_line = re.sub(
                string_pattern,
                self.fallback_colors['string'] + r'\g<0>' + self.fallback_colors['reset'],
                highlighted_line
            )
            
            # Highlight keywords
            for keyword in keywords:
                pattern = r'\b' + re.escape(keyword) + r'\b'
                highlighted_line = re.sub(
                    pattern,
                    self.fallback_colors['keyword'] + keyword + self.fallback_colors['reset'],
                    highlighted_line
                )
            
            highlighted_lines.append(highlighted_line)
        
        return '\n'.join(highlighted_lines)
    
    def _get_language_keywords(self, language: str) -> List[str]:
        """Get keywords for a programming language."""
        keyword_map = {
            'python': [
                'def', 'class', 'if', 'else', 'elif', 'for', 'while', 'try', 'except',
                'finally', 'with', 'as', 'import', 'from', 'return', 'yield', 'lambda',
                'and', 'or', 'not', 'in', 'is', 'True', 'False', 'None'
            ],
            'javascript': [
                'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'do',
                'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
                'finally', 'throw', 'new', 'this', 'typeof', 'instanceof', 'true', 'false',
                'null', 'undefined'
            ],
            'typescript': [
                'function', 'var', 'let', 'const', 'if', 'else', 'for', 'while', 'do',
                'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
                'finally', 'throw', 'new', 'this', 'typeof', 'instanceof', 'true', 'false',
                'null', 'undefined', 'interface', 'type', 'enum', 'class', 'extends',
                'implements', 'public', 'private', 'protected', 'readonly'
            ],
            'java': [
                'public', 'private', 'protected', 'static', 'final', 'abstract', 'class',
                'interface', 'extends', 'implements', 'if', 'else', 'for', 'while', 'do',
                'switch', 'case', 'default', 'break', 'continue', 'return', 'try', 'catch',
                'finally', 'throw', 'throws', 'new', 'this', 'super', 'true', 'false', 'null'
            ],
            'c': [
                'int', 'char', 'float', 'double', 'void', 'if', 'else', 'for', 'while',
                'do', 'switch', 'case', 'default', 'break', 'continue', 'return', 'struct',
                'union', 'enum', 'typedef', 'static', 'extern', 'const', 'volatile'
            ],
            'c++': [
                'int', 'char', 'float', 'double', 'void', 'bool', 'if', 'else', 'for',
                'while', 'do', 'switch', 'case', 'default', 'break', 'continue', 'return',
                'class', 'struct', 'union', 'enum', 'typedef', 'static', 'extern', 'const',
                'volatile', 'public', 'private', 'protected', 'virtual', 'override', 'new',
                'delete', 'this', 'true', 'false', 'nullptr'
            ],
            'rust': [
                'fn', 'let', 'mut', 'const', 'static', 'if', 'else', 'match', 'for',
                'while', 'loop', 'break', 'continue', 'return', 'struct', 'enum', 'impl',
                'trait', 'pub', 'mod', 'use', 'crate', 'self', 'super', 'true', 'false'
            ],
            'go': [
                'func', 'var', 'const', 'type', 'struct', 'interface', 'if', 'else',
                'for', 'range', 'switch', 'case', 'default', 'break', 'continue', 'return',
                'go', 'defer', 'select', 'chan', 'map', 'true', 'false', 'nil'
            ]
        }
        
        return keyword_map.get(language, [])
    
    def highlight_code_block(self, text: str) -> str:
        """
        Highlight code blocks in markdown-style text.
        
        Args:
            text: Text containing code blocks
        
        Returns:
            Text with highlighted code blocks
        """
        import re
        
        # Pattern for fenced code blocks
        pattern = r'```(\w+)?\n(.*?)\n```'
        
        def highlight_match(match):
            language = match.group(1) or 'text'
            code = match.group(2)
            
            highlighted = self.highlight_code(code, language)
            
            # Convert to string if needed
            if hasattr(highlighted, '__str__'):
                highlighted = str(highlighted)
            
            return f"```{language}\n{highlighted}\n```"
        
        return re.sub(pattern, highlight_match, text, flags=re.DOTALL)
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages."""
        languages = list(self.language_aliases.keys())
        languages.extend(self.language_aliases.values())
        
        # Add common languages
        languages.extend([
            'python', 'javascript', 'typescript', 'java', 'c', 'c++', 'rust', 'go',
            'html', 'css', 'json', 'yaml', 'markdown', 'bash', 'sql', 'xml'
        ])
        
        return sorted(list(set(languages)))
    
    def detect_language(self, code: str, filename: Optional[str] = None) -> str:
        """
        Detect programming language from code content.
        
        Args:
            code: Code content
            filename: Optional filename for extension-based detection
        
        Returns:
            Detected language name
        """
        # Try filename extension first
        if filename:
            import os
            ext = os.path.splitext(filename)[1].lower()
            if ext.startswith('.'):
                ext = ext[1:]
            
            if ext in self.language_aliases:
                return self.language_aliases[ext]
        
        # Try Pygments lexer guessing
        if PYGMENTS_AVAILABLE:
            try:
                lexer = guess_lexer(code)
                return lexer.name.lower()
            except:
                pass
        
        # Simple heuristics
        if 'def ' in code and 'import ' in code:
            return 'python'
        elif 'function ' in code and ('var ' in code or 'let ' in code):
            return 'javascript'
        elif '#include' in code and 'int main' in code:
            return 'c'
        elif 'public class' in code and 'public static void main' in code:
            return 'java'
        elif '<html' in code or '<!DOCTYPE' in code:
            return 'html'
        elif 'SELECT' in code.upper() and 'FROM' in code.upper():
            return 'sql'
        
        return 'text'
