"""
Enhanced Memory System for AI Coding Agent.

This module provides comprehensive memory capabilities including:
- Conversation memory with intelligent context retention
- Project memory for workspace-specific information
- Learning memory for pattern recognition and improvement
- Cross-session memory persistence
- Intelligent memory compression and optimization
"""

import json
import logging
import sqlite3
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Tu<PERSON>
from pathlib import Path
from dataclasses import dataclass, field, asdict
from enum import Enum

from config import get_config
from message import Message
from conversation import ConversationManager

logger = logging.getLogger(__name__)


class MemoryScope(Enum):
    """Scope of memory storage."""
    SESSION = "session"          # Current session only
    CONVERSATION = "conversation" # Current conversation
    PROJECT = "project"          # Current project/workspace
    GLOBAL = "global"            # Across all sessions
    USER = "user"               # User-specific across projects


class MemoryPriority(Enum):
    """Priority levels for memory retention."""
    CRITICAL = "critical"    # Never delete
    HIGH = "high"           # Keep for extended periods
    MEDIUM = "medium"       # Standard retention
    LOW = "low"            # Delete when space needed
    TEMPORARY = "temporary" # Delete after session


@dataclass
class EnhancedMemory:
    """Enhanced memory entry with additional metadata."""
    id: str
    content: str
    memory_type: str
    scope: MemoryScope
    priority: MemoryPriority
    created_at: datetime
    last_accessed: datetime
    access_count: int
    importance_score: float  # 0.0 to 1.0
    
    # Context information
    conversation_id: Optional[str] = None
    project_path: Optional[str] = None
    workspace_context: Optional[str] = None
    
    # Relationships
    related_memories: List[str] = field(default_factory=list)
    parent_memory: Optional[str] = None
    child_memories: List[str] = field(default_factory=list)
    
    # Content analysis
    keywords: List[str] = field(default_factory=list)
    entities: List[str] = field(default_factory=list)
    sentiment: Optional[str] = None
    
    # Learning metadata
    success_rate: float = 0.0  # How often this memory led to successful outcomes
    feedback_score: float = 0.0  # User feedback on this memory's usefulness
    
    # Additional metadata
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


class EnhancedMemorySystem:
    """
    Enhanced memory system with intelligent context retention.
    
    Features:
    - Multi-scope memory storage (session, conversation, project, global)
    - Intelligent memory compression and optimization
    - Context-aware memory retrieval
    - Learning from user feedback
    - Cross-session memory persistence
    - Memory relationship mapping
    """
    
    def __init__(self, workspace_path: Optional[str] = None):
        """
        Initialize the enhanced memory system.
        
        Args:
            workspace_path: Current workspace path for project-scoped memory
        """
        self.config = get_config()
        self.workspace_path = workspace_path
        
        # Memory storage
        self.session_memory: Dict[str, EnhancedMemory] = {}
        self.conversation_memory: Dict[str, EnhancedMemory] = {}
        
        # Database for persistent memory
        self.db_path = self.config.data_dir / "enhanced_memory.db"
        self.config.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Memory management settings
        self.max_session_memories = 100
        self.max_conversation_memories = 500
        self.max_project_memories = 1000
        self.max_global_memories = 5000
        
        # Context tracking
        self.current_conversation_id: Optional[str] = None
        self.current_session_id = self._generate_session_id()
        
        # Learning metrics
        self.memory_usage_stats = {
            "memories_created": 0,
            "memories_accessed": 0,
            "successful_retrievals": 0,
            "failed_retrievals": 0
        }
    
    def _init_database(self) -> None:
        """Initialize the memory database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS enhanced_memories (
                    id TEXT PRIMARY KEY,
                    content TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    scope TEXT NOT NULL,
                    priority TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    last_accessed TEXT NOT NULL,
                    access_count INTEGER DEFAULT 0,
                    importance_score REAL DEFAULT 0.5,
                    conversation_id TEXT,
                    project_path TEXT,
                    workspace_context TEXT,
                    related_memories TEXT,
                    parent_memory TEXT,
                    child_memories TEXT,
                    keywords TEXT,
                    entities TEXT,
                    sentiment TEXT,
                    success_rate REAL DEFAULT 0.0,
                    feedback_score REAL DEFAULT 0.0,
                    tags TEXT,
                    metadata TEXT
                )
            """)
            
            # Create indexes for efficient querying
            conn.execute("CREATE INDEX IF NOT EXISTS idx_scope ON enhanced_memories(scope)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_type ON enhanced_memories(memory_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_project ON enhanced_memories(project_path)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_conversation ON enhanced_memories(conversation_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_importance ON enhanced_memories(importance_score)")
            
            # Full-text search
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS memories_search USING fts5(
                    id, content, keywords, entities, tags, content=enhanced_memories
                )
            """)
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return f"session_{int(datetime.now().timestamp())}"
    
    def store_memory(
        self,
        content: str,
        memory_type: str = "context",
        scope: MemoryScope = MemoryScope.CONVERSATION,
        priority: MemoryPriority = MemoryPriority.MEDIUM,
        importance_score: float = 0.5,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Store a memory with enhanced metadata.
        
        Args:
            content: Memory content
            memory_type: Type of memory
            scope: Memory scope
            priority: Memory priority
            importance_score: Importance score (0.0 to 1.0)
            tags: Optional tags
            metadata: Optional metadata
        
        Returns:
            Memory ID
        """
        # Generate memory ID
        memory_id = hashlib.md5(
            f"{content}{memory_type}{datetime.now().isoformat()}".encode()
        ).hexdigest()
        
        # Extract keywords and entities (simple implementation)
        keywords = self._extract_keywords(content)
        entities = self._extract_entities(content)
        
        # Create enhanced memory
        memory = EnhancedMemory(
            id=memory_id,
            content=content,
            memory_type=memory_type,
            scope=scope,
            priority=priority,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=0,
            importance_score=importance_score,
            conversation_id=self.current_conversation_id,
            project_path=self.workspace_path,
            keywords=keywords,
            entities=entities,
            tags=tags or [],
            metadata=metadata or {}
        )
        
        # Store based on scope
        if scope == MemoryScope.SESSION:
            self.session_memory[memory_id] = memory
        elif scope == MemoryScope.CONVERSATION:
            self.conversation_memory[memory_id] = memory
        else:
            # Store in database for persistent scopes
            self._store_persistent_memory(memory)
        
        self.memory_usage_stats["memories_created"] += 1
        logger.debug(f"Stored memory {memory_id} with scope {scope.value}")
        
        return memory_id
    
    def _store_persistent_memory(self, memory: EnhancedMemory) -> None:
        """Store memory in persistent database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT OR REPLACE INTO enhanced_memories 
                (id, content, memory_type, scope, priority, created_at, last_accessed,
                 access_count, importance_score, conversation_id, project_path,
                 workspace_context, related_memories, parent_memory, child_memories,
                 keywords, entities, sentiment, success_rate, feedback_score, tags, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                memory.id, memory.content, memory.memory_type, memory.scope.value,
                memory.priority.value, memory.created_at.isoformat(),
                memory.last_accessed.isoformat(), memory.access_count,
                memory.importance_score, memory.conversation_id, memory.project_path,
                memory.workspace_context, json.dumps(memory.related_memories),
                memory.parent_memory, json.dumps(memory.child_memories),
                json.dumps(memory.keywords), json.dumps(memory.entities),
                memory.sentiment, memory.success_rate, memory.feedback_score,
                json.dumps(memory.tags), json.dumps(memory.metadata)
            ))
    
    def retrieve_memories(
        self,
        query: Optional[str] = None,
        memory_type: Optional[str] = None,
        scope: Optional[MemoryScope] = None,
        limit: int = 10,
        min_importance: float = 0.0
    ) -> List[EnhancedMemory]:
        """
        Retrieve memories based on criteria.
        
        Args:
            query: Search query
            memory_type: Filter by memory type
            scope: Filter by scope
            limit: Maximum number of results
            min_importance: Minimum importance score
        
        Returns:
            List of matching memories
        """
        memories = []
        
        # Search session memory
        if scope is None or scope == MemoryScope.SESSION:
            memories.extend(self._search_memory_dict(
                self.session_memory, query, memory_type, min_importance
            ))
        
        # Search conversation memory
        if scope is None or scope == MemoryScope.CONVERSATION:
            memories.extend(self._search_memory_dict(
                self.conversation_memory, query, memory_type, min_importance
            ))
        
        # Search persistent memory
        if scope is None or scope in [MemoryScope.PROJECT, MemoryScope.GLOBAL, MemoryScope.USER]:
            memories.extend(self._search_persistent_memory(
                query, memory_type, scope, min_importance
            ))
        
        # Sort by relevance and importance
        memories.sort(key=lambda m: (m.importance_score, m.access_count), reverse=True)
        
        # Update access statistics
        for memory in memories[:limit]:
            memory.last_accessed = datetime.now()
            memory.access_count += 1
        
        self.memory_usage_stats["memories_accessed"] += len(memories[:limit])
        
        return memories[:limit]
    
    def _search_memory_dict(
        self,
        memory_dict: Dict[str, EnhancedMemory],
        query: Optional[str],
        memory_type: Optional[str],
        min_importance: float
    ) -> List[EnhancedMemory]:
        """Search memories in a dictionary."""
        results = []
        
        for memory in memory_dict.values():
            # Filter by importance
            if memory.importance_score < min_importance:
                continue
            
            # Filter by type
            if memory_type and memory.memory_type != memory_type:
                continue
            
            # Filter by query
            if query:
                query_lower = query.lower()
                if not (query_lower in memory.content.lower() or
                       any(query_lower in keyword.lower() for keyword in memory.keywords) or
                       any(query_lower in tag.lower() for tag in memory.tags)):
                    continue
            
            results.append(memory)
        
        return results
    
    def _search_persistent_memory(
        self,
        query: Optional[str],
        memory_type: Optional[str],
        scope: Optional[MemoryScope],
        min_importance: float
    ) -> List[EnhancedMemory]:
        """Search persistent memory database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            
            sql = "SELECT * FROM enhanced_memories WHERE importance_score >= ?"
            params = [min_importance]
            
            if memory_type:
                sql += " AND memory_type = ?"
                params.append(memory_type)
            
            if scope:
                sql += " AND scope = ?"
                params.append(scope.value)
            
            if self.workspace_path and scope == MemoryScope.PROJECT:
                sql += " AND project_path = ?"
                params.append(self.workspace_path)
            
            if query:
                # Use simple text search for now (can be enhanced with FTS later)
                sql += " AND (content LIKE ? OR keywords LIKE ? OR tags LIKE ?)"
                query_pattern = f"%{query}%"
                params.extend([query_pattern, query_pattern, query_pattern])
            
            sql += " ORDER BY importance_score DESC, access_count DESC"
            
            cursor = conn.execute(sql, params)
            rows = cursor.fetchall()
            
            memories = []
            for row in rows:
                memory = self._row_to_memory(row)
                memories.append(memory)
            
            return memories
    
    def _row_to_memory(self, row: sqlite3.Row) -> EnhancedMemory:
        """Convert database row to EnhancedMemory object."""
        return EnhancedMemory(
            id=row['id'],
            content=row['content'],
            memory_type=row['memory_type'],
            scope=MemoryScope(row['scope']),
            priority=MemoryPriority(row['priority']),
            created_at=datetime.fromisoformat(row['created_at']),
            last_accessed=datetime.fromisoformat(row['last_accessed']),
            access_count=row['access_count'],
            importance_score=row['importance_score'],
            conversation_id=row['conversation_id'],
            project_path=row['project_path'],
            workspace_context=row['workspace_context'],
            related_memories=json.loads(row['related_memories'] or '[]'),
            parent_memory=row['parent_memory'],
            child_memories=json.loads(row['child_memories'] or '[]'),
            keywords=json.loads(row['keywords'] or '[]'),
            entities=json.loads(row['entities'] or '[]'),
            sentiment=row['sentiment'],
            success_rate=row['success_rate'],
            feedback_score=row['feedback_score'],
            tags=json.loads(row['tags'] or '[]'),
            metadata=json.loads(row['metadata'] or '{}')
        )
    
    def _extract_keywords(self, content: str) -> List[str]:
        """Extract keywords from content (simple implementation)."""
        # Simple keyword extraction - can be enhanced with NLP
        import re
        words = re.findall(r'\b\w+\b', content.lower())
        
        # Filter out common words
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        
        keywords = [word for word in words if len(word) > 3 and word not in stop_words]
        
        # Return top keywords by frequency
        from collections import Counter
        return [word for word, count in Counter(keywords).most_common(10)]
    
    def _extract_entities(self, content: str) -> List[str]:
        """Extract entities from content (simple implementation)."""
        # Simple entity extraction - can be enhanced with NLP
        import re
        
        entities = []
        
        # Extract file paths
        file_paths = re.findall(r'[./][\w/.-]+\.\w+', content)
        entities.extend(file_paths)
        
        # Extract URLs
        urls = re.findall(r'https?://[\w.-]+', content)
        entities.extend(urls)
        
        # Extract code-like patterns
        code_patterns = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\(\)', content)
        entities.extend(code_patterns)
        
        return list(set(entities))
    
    def set_conversation_context(self, conversation_id: str) -> None:
        """Set the current conversation context."""
        self.current_conversation_id = conversation_id
    
    def clear_session_memory(self) -> None:
        """Clear session memory."""
        self.session_memory.clear()
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM enhanced_memories")
            persistent_count = cursor.fetchone()[0]
        
        return {
            "session_memories": len(self.session_memory),
            "conversation_memories": len(self.conversation_memory),
            "persistent_memories": persistent_count,
            "current_session_id": self.current_session_id,
            "current_conversation_id": self.current_conversation_id,
            "workspace_path": self.workspace_path,
            **self.memory_usage_stats
        }


# Global memory system instance
_memory_system: Optional[EnhancedMemorySystem] = None


def get_memory_system(workspace_path: Optional[str] = None) -> EnhancedMemorySystem:
    """Get the global memory system instance."""
    global _memory_system
    if _memory_system is None:
        _memory_system = EnhancedMemorySystem(workspace_path)
    return _memory_system


def remember(content: str, memory_type: str = "context", **kwargs) -> str:
    """Convenience function to store a memory."""
    memory_system = get_memory_system()
    return memory_system.store_memory(content, memory_type, **kwargs)


def recall(query: str, **kwargs) -> List[EnhancedMemory]:
    """Convenience function to retrieve memories."""
    memory_system = get_memory_system()
    return memory_system.retrieve_memories(query, **kwargs)
