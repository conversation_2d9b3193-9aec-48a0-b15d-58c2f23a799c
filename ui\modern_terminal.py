"""
Modern Terminal Interface for Enhanced AI Coding Agent.

This module provides a beautiful, modern terminal interface similar to claudecode
with real-time chat, syntax highlighting, file explorer, and terminal integration.
"""

import asyncio
import logging
import threading
from typing import Optional, Dict, Any, List, Callable
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

try:
    from rich.console import Console
    from rich.layout import Layout
    from rich.panel import Panel
    from rich.text import Text
    from rich.live import Live
    from rich.table import Table
    from rich.tree import Tree
    from rich.syntax import Syntax
    from rich.markdown import Markdown
    from rich.progress import Progress, SpinnerColumn, TextColumn
    from rich.prompt import Prompt
    from rich.align import Align
    from rich.columns import Columns
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

try:
    from textual.app import App, ComposeResult
    from textual.containers import Container, Horizontal, Vertical
    from textual.widgets import (
        Header, Footer, Input, Log, Tree as TextualTree,
        Static, Button, TextArea, Tabs, Tab, DataTable
    )
    from textual.reactive import reactive
    from textual.message import Message
    from textual.binding import Binding
    TEXTUAL_AVAILABLE = True
except ImportError:
    TEXTUAL_AVAILABLE = False

from ui.chat_interface import ChatInterface
from ui.file_explorer import FileExplorer
from ui.code_editor import CodeEditor
from ui.syntax_highlighter import SyntaxHighlighter

logger = logging.getLogger(__name__)


class UITheme(Enum):
    """UI theme options."""
    DARK = "dark"
    LIGHT = "light"
    AUTO = "auto"
    CLAUDE = "claude"  # Claude-inspired theme


class LayoutMode(Enum):
    """Layout mode options."""
    SPLIT = "split"      # Split pane layout
    TABBED = "tabbed"    # Tabbed interface
    MINIMAL = "minimal"  # Minimal single pane
    FULL = "full"        # Full-featured layout


@dataclass
class UIConfig:
    """Configuration for the modern UI."""
    theme: UITheme = UITheme.DARK
    layout: LayoutMode = LayoutMode.SPLIT
    workspace_path: Optional[str] = None
    auto_save: bool = True
    syntax_highlighting: bool = True
    file_tree_visible: bool = True
    terminal_visible: bool = True
    chat_history_limit: int = 1000
    font_size: int = 14
    font_family: str = "JetBrains Mono"


class ModernTerminalInterface:
    """
    Modern terminal interface with claudecode-inspired design.
    
    Features:
    - Beautiful, clean interface with modern styling
    - Real-time chat with syntax highlighting
    - Integrated file explorer with tree view
    - Code editor with syntax highlighting
    - Terminal integration
    - Responsive layout
    """
    
    def __init__(
        self,
        workspace_path: Optional[str] = None,
        theme: str = "dark",
        layout: str = "split",
        **kwargs
    ):
        """
        Initialize the modern terminal interface.
        
        Args:
            workspace_path: Path to the workspace directory
            theme: UI theme
            layout: Layout mode
            **kwargs: Additional configuration options
        """
        self.config = UIConfig(
            theme=UITheme(theme),
            layout=LayoutMode(layout),
            workspace_path=workspace_path,
            **kwargs
        )
        
        self.console = Console() if RICH_AVAILABLE else None
        self.chat_interface = None
        self.file_explorer = None
        self.code_editor = None
        self.syntax_highlighter = None
        
        self._running = False
        self._app = None
        
        self._initialize_components()
    
    def _initialize_components(self) -> None:
        """Initialize UI components."""
        try:
            # Initialize chat interface
            self.chat_interface = ChatInterface(
                theme=self.config.theme,
                history_limit=self.config.chat_history_limit
            )
            
            # Initialize file explorer
            if self.config.workspace_path:
                self.file_explorer = FileExplorer(
                    root_path=self.config.workspace_path,
                    theme=self.config.theme
                )
            
            # Initialize code editor
            self.code_editor = CodeEditor(
                theme=self.config.theme,
                font_size=self.config.font_size,
                font_family=self.config.font_family
            )
            
            # Initialize syntax highlighter
            if self.config.syntax_highlighting:
                self.syntax_highlighter = SyntaxHighlighter(
                    theme=self.config.theme
                )
            
            logger.info("Modern UI components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize UI components: {e}")
            raise
    
    def launch(self, host: str = "localhost", port: int = 8080) -> None:
        """
        Launch the modern terminal interface.
        
        Args:
            host: Host to bind to
            port: Port to bind to
        """
        if TEXTUAL_AVAILABLE:
            self._launch_textual_app()
        elif RICH_AVAILABLE:
            self._launch_rich_interface()
        else:
            self._launch_fallback_interface()
    
    def _launch_textual_app(self) -> None:
        """Launch the Textual-based modern interface."""
        try:
            app = ModernTerminalApp(interface=self)
            app.run()
        except Exception as e:
            logger.error(f"Failed to launch Textual app: {e}")
            self._launch_rich_interface()
    
    def _launch_rich_interface(self) -> None:
        """Launch the Rich-based interface."""
        if not RICH_AVAILABLE:
            self._launch_fallback_interface()
            return
        
        try:
            self._run_rich_interface()
        except Exception as e:
            logger.error(f"Failed to launch Rich interface: {e}")
            self._launch_fallback_interface()
    
    def _launch_fallback_interface(self) -> None:
        """Launch a simple fallback interface."""
        print("🚀 Enhanced AI Coding Agent - Modern Terminal Interface")
        print("=" * 60)
        print("Note: Install 'rich' and 'textual' for the full modern UI experience")
        print("pip install rich textual")
        print("=" * 60)
        
        # Simple chat loop
        while True:
            try:
                user_input = input("\n💬 You: ")
                if user_input.lower() in ['/exit', '/quit', 'exit', 'quit']:
                    break
                
                print(f"🤖 Assistant: Processing '{user_input}'...")
                # Here we would integrate with the chat system
                
            except (EOFError, KeyboardInterrupt):
                break
        
        print("\n👋 Goodbye!")


if TEXTUAL_AVAILABLE:
    class ModernTerminalApp(App):
        """Textual-based modern terminal application."""
        
        CSS = """
        Screen {
            background: #0d1117;
        }
        
        .header {
            background: #161b22;
            color: #f0f6fc;
            height: 3;
        }
        
        .sidebar {
            background: #0d1117;
            border: solid #30363d;
            width: 25%;
        }
        
        .main-content {
            background: #0d1117;
            border: solid #30363d;
        }
        
        .chat-area {
            background: #161b22;
            border: solid #30363d;
            height: 70%;
        }
        
        .input-area {
            background: #0d1117;
            border: solid #30363d;
            height: 30%;
        }
        
        .file-tree {
            background: #0d1117;
            color: #f0f6fc;
        }
        """
        
        BINDINGS = [
            Binding("ctrl+c", "quit", "Quit"),
            Binding("ctrl+t", "toggle_tree", "Toggle File Tree"),
            Binding("ctrl+n", "new_file", "New File"),
            Binding("ctrl+o", "open_file", "Open File"),
        ]
        
        def __init__(self, interface: ModernTerminalInterface):
            super().__init__()
            self.interface = interface
        
        def compose(self) -> ComposeResult:
            """Compose the application layout."""
            yield Header(show_clock=True)
            
            with Horizontal():
                # Sidebar with file explorer
                with Vertical(classes="sidebar"):
                    yield Static("📁 File Explorer", classes="file-tree")
                    if self.interface.file_explorer:
                        yield TextualTree("Workspace")
                
                # Main content area
                with Vertical(classes="main-content"):
                    # Chat area
                    with Container(classes="chat-area"):
                        yield Log(id="chat-log")
                    
                    # Input area
                    with Container(classes="input-area"):
                        yield Input(placeholder="Type your message here...", id="chat-input")
            
            yield Footer()
        
        def action_toggle_tree(self) -> None:
            """Toggle file tree visibility."""
            # Implementation for toggling file tree
            pass
        
        def action_new_file(self) -> None:
            """Create a new file."""
            # Implementation for creating new file
            pass
        
        def action_open_file(self) -> None:
            """Open a file."""
            # Implementation for opening file
            pass
