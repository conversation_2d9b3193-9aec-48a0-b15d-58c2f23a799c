"""
Test suite for the Enhanced AI Coding Agent.

This module provides comprehensive tests for all enhanced capabilities including:
- Sequential thinking and Chain of Thought reasoning
- Enhanced tool discovery and integration
- Advanced research capabilities
- Multi-file coordination and project-level reasoning
- Agent orchestration and mode switching
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import the enhanced components
from enhanced_agent_orchestrator import (
    EnhancedAgentOrchestrator, 
    AgentMode, 
    TaskComplexity,
    get_enhanced_orchestrator
)
from tools.enhanced_tool_discovery import (
    EnhancedToolDiscovery,
    ToolCategory,
    ToolContext,
    ToolMetadata,
    get_enhanced_discovery
)
from tools.sequential_thinking import (
    SequentialThinkingTool,
    ThinkingType,
    ThinkingStep
)
from tools.enhanced_research import (
    EnhancedResearchTool,
    ResearchType,
    SourceType
)
from message import Message


class TestEnhancedAgentOrchestrator(unittest.TestCase):
    """Test cases for the Enhanced Agent Orchestrator."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.orchestrator = EnhancedAgentOrchestrator()
        self.test_workspace = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_task_complexity_analysis(self):
        """Test task complexity analysis."""
        # Simple task
        simple_messages = list(self.orchestrator._analyze_task_complexity("What is Python?"))
        self.assertTrue(any("Simple" in msg.content for msg in simple_messages))
        self.assertEqual(self.orchestrator.active_context.task_complexity, TaskComplexity.SIMPLE)
        
        # Complex task
        self.orchestrator.active_context.task_complexity = TaskComplexity.MODERATE  # Reset
        complex_messages = list(self.orchestrator._analyze_task_complexity(
            "Design and implement a comprehensive microservices architecture with advanced caching, "
            "load balancing, and real-time monitoring capabilities"
        ))
        self.assertTrue(any("Expert" in msg.content for msg in complex_messages))
        self.assertEqual(self.orchestrator.active_context.task_complexity, TaskComplexity.EXPERT)
    
    def test_agent_mode_determination(self):
        """Test agent mode determination."""
        # Research mode
        research_messages = list(self.orchestrator._determine_agent_mode(
            "Research the latest best practices for React development"
        ))
        self.assertEqual(self.orchestrator.active_context.agent_mode, AgentMode.RESEARCH)
        
        # Analysis mode
        analysis_messages = list(self.orchestrator._determine_agent_mode(
            "Analyze this codebase for potential improvements"
        ))
        self.assertEqual(self.orchestrator.active_context.agent_mode, AgentMode.ANALYSIS)
        
        # Development mode
        dev_messages = list(self.orchestrator._determine_agent_mode(
            "Build a REST API with authentication"
        ))
        self.assertEqual(self.orchestrator.active_context.agent_mode, AgentMode.DEVELOPMENT)
    
    def test_tool_recommendations(self):
        """Test tool recommendation system."""
        with patch.object(self.orchestrator.tool_discovery, 'recommend_tools') as mock_recommend:
            mock_recommend.return_value = [
                Mock(tool_name="python", confidence=0.9, reasoning="High confidence for Python task"),
                Mock(tool_name="file_ops", confidence=0.8, reasoning="File operations needed")
            ]
            
            messages = list(self.orchestrator._get_tool_recommendations("Create a Python script"))
            
            # Check that recommendations were processed
            self.assertTrue(any("Recommended Tools" in msg.content for msg in messages))
            self.assertIn("python", self.orchestrator.active_context.active_tools)
            self.assertIn("file_ops", self.orchestrator.active_context.active_tools)
    
    def test_performance_metrics_update(self):
        """Test performance metrics tracking."""
        initial_tasks = self.orchestrator.performance_metrics["tasks_completed"]
        initial_confidence = self.orchestrator.performance_metrics["average_confidence"]
        
        self.orchestrator.active_context.confidence_score = 0.9
        self.orchestrator.active_context.active_tools = ["python", "file_ops"]
        
        self.orchestrator._update_performance_metrics()
        
        # Check metrics were updated
        self.assertEqual(
            self.orchestrator.performance_metrics["tasks_completed"], 
            initial_tasks + 1
        )
        self.assertGreater(
            self.orchestrator.performance_metrics["average_confidence"],
            initial_confidence
        )
        self.assertEqual(
            self.orchestrator.performance_metrics["tool_usage_stats"]["python"],
            1
        )
    
    def test_enhanced_system_prompt_creation(self):
        """Test enhanced system prompt creation."""
        self.orchestrator.active_context.task_complexity = TaskComplexity.COMPLEX
        self.orchestrator.active_context.agent_mode = AgentMode.DEVELOPMENT
        self.orchestrator.active_context.active_tools = ["python", "file_ops"]
        self.orchestrator.active_context.confidence_score = 0.85
        
        prompt = self.orchestrator._create_enhanced_system_prompt()
        
        # Check that context information is included
        self.assertIn("complex", prompt.lower())
        self.assertIn("development", prompt.lower())
        self.assertIn("python", prompt)
        self.assertIn("85.0%", prompt)


class TestEnhancedToolDiscovery(unittest.TestCase):
    """Test cases for Enhanced Tool Discovery."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.discovery = EnhancedToolDiscovery()
    
    def test_tool_metadata_registration(self):
        """Test tool metadata registration."""
        metadata = ToolMetadata(
            category=ToolCategory.CODE_ANALYSIS,
            contexts=[ToolContext.DEBUGGING],
            confidence_score=0.9
        )
        
        self.discovery.register_tool_metadata("test_tool", metadata)
        
        self.assertIn("test_tool", self.discovery.tool_metadata)
        self.assertEqual(
            self.discovery.tool_metadata["test_tool"].category,
            ToolCategory.CODE_ANALYSIS
        )
    
    def test_tool_recommendations(self):
        """Test tool recommendation generation."""
        # Register some test metadata
        self.discovery.register_tool_metadata("python", ToolMetadata(
            category=ToolCategory.DEVELOPMENT,
            contexts=[ToolContext.FEATURE_DEVELOPMENT],
            confidence_score=0.9,
            success_rate=0.95
        ))
        
        recommendations = self.discovery.recommend_tools(
            context="Create a Python function for data processing",
            max_recommendations=3
        )
        
        self.assertIsInstance(recommendations, list)
        if recommendations:  # Only test if recommendations were generated
            self.assertTrue(all(hasattr(rec, 'tool_name') for rec in recommendations))
            self.assertTrue(all(hasattr(rec, 'confidence') for rec in recommendations))
    
    def test_usage_recording(self):
        """Test tool usage recording."""
        # Register metadata first
        self.discovery.register_tool_metadata("test_tool", ToolMetadata(
            category=ToolCategory.DEVELOPMENT,
            usage_frequency=0,
            success_rate=1.0
        ))
        
        # Record usage
        self.discovery.record_tool_usage(
            tool_name="test_tool",
            success=True,
            execution_time=2.5,
            context="test context",
            parameters={"param1": "value1"}
        )
        
        # Check that metadata was updated
        metadata = self.discovery.tool_metadata["test_tool"]
        self.assertEqual(metadata.usage_frequency, 1)
        self.assertEqual(metadata.success_rate, 1.0)
        self.assertEqual(metadata.average_execution_time, 2.5)


class TestSequentialThinkingTool(unittest.TestCase):
    """Test cases for Sequential Thinking Tool."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.thinking_tool = SequentialThinkingTool()
    
    def test_chain_of_thought_execution(self):
        """Test Chain of Thought reasoning execution."""
        messages = list(self.thinking_tool.execute(
            content="How can I optimize a slow database query?",
            thinking_type=ThinkingType.CHAIN_OF_THOUGHT.value,
            max_iterations=5
        ))
        
        # Check that thinking process messages were generated
        self.assertTrue(len(messages) > 0)
        self.assertTrue(any("Starting Sequential Thinking" in msg.content for msg in messages))
        self.assertTrue(any("Thinking Process Complete" in msg.content for msg in messages))
    
    def test_self_critique_execution(self):
        """Test self-critique process."""
        messages = list(self.thinking_tool.execute(
            content="Design a caching strategy for a web application",
            thinking_type=ThinkingType.SELF_CRITIQUE.value,
            max_iterations=3
        ))
        
        # Check for critique-specific content
        self.assertTrue(any("critique" in msg.content.lower() for msg in messages))
    
    def test_problem_decomposition(self):
        """Test problem decomposition capability."""
        messages = list(self.thinking_tool.execute(
            content="Build a complete e-commerce platform",
            thinking_type=ThinkingType.PROBLEM_DECOMPOSITION.value
        ))
        
        # Check for decomposition-specific content
        self.assertTrue(any("component" in msg.content.lower() for msg in messages))


class TestEnhancedResearchTool(unittest.TestCase):
    """Test cases for Enhanced Research Tool."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.research_tool = EnhancedResearchTool()
    
    def test_web_search_execution(self):
        """Test web search functionality."""
        with patch.object(self.research_tool, '_google_search') as mock_google, \
             patch.object(self.research_tool, '_duckduckgo_search') as mock_ddg, \
             patch.object(self.research_tool, '_bing_search') as mock_bing:
            
            # Mock search results
            mock_google.return_value = []
            mock_ddg.return_value = []
            mock_bing.return_value = []
            
            messages = list(self.research_tool.execute(
                content="Python best practices 2024",
                research_type=ResearchType.WEB_SEARCH.value,
                max_sources=5
            ))
            
            # Check that research process was initiated
            self.assertTrue(any("Starting Enhanced Research" in msg.content for msg in messages))
    
    def test_documentation_search(self):
        """Test documentation search functionality."""
        messages = list(self.research_tool.execute(
            content="React hooks documentation",
            research_type=ResearchType.DOCUMENTATION.value,
            language="javascript"
        ))
        
        # Check for documentation-specific processing
        self.assertTrue(any("documentation" in msg.content.lower() for msg in messages))
    
    def test_cache_functionality(self):
        """Test research result caching."""
        # First search
        query = "test query for caching"
        cache_key = self.research_tool._generate_cache_key(
            query, ResearchType.WEB_SEARCH, "python"
        )
        
        # Verify cache key generation
        self.assertIsInstance(cache_key, str)
        self.assertIn("test", cache_key.lower())


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete enhanced agent system."""
    
    def setUp(self):
        """Set up integration test fixtures."""
        self.orchestrator = get_enhanced_orchestrator()
        self.test_workspace = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up integration test fixtures."""
        import shutil
        shutil.rmtree(self.test_workspace, ignore_errors=True)
    
    def test_end_to_end_simple_request(self):
        """Test end-to-end processing of a simple request."""
        with patch('enhanced_agent_orchestrator.init_tools'), \
             patch('enhanced_agent_orchestrator.get_tools_for_llm'), \
             patch('enhanced_agent_orchestrator.generate_response') as mock_generate:
            
            mock_generate.return_value = [Message(role="assistant", content="Test response")]
            
            messages = list(self.orchestrator.process_user_request(
                user_input="What is Python?",
                workspace_path=self.test_workspace
            ))
            
            # Check that processing completed
            self.assertTrue(len(messages) > 0)
            self.assertTrue(any("Enhanced AI Agent Activated" in msg.content for msg in messages))
    
    def test_end_to_end_complex_request(self):
        """Test end-to-end processing of a complex request."""
        with patch('enhanced_agent_orchestrator.init_tools'), \
             patch('enhanced_agent_orchestrator.get_tools_for_llm'), \
             patch('enhanced_agent_orchestrator.generate_response') as mock_generate:
            
            mock_generate.return_value = [Message(role="assistant", content="Complex response")]
            
            messages = list(self.orchestrator.process_user_request(
                user_input="Design and implement a comprehensive microservices architecture",
                workspace_path=self.test_workspace
            ))
            
            # Check that complex processing was triggered
            self.assertTrue(len(messages) > 0)
            self.assertTrue(any("Chain of Thought" in msg.content for msg in messages))
    
    def test_performance_summary(self):
        """Test performance summary generation."""
        summary = self.orchestrator.get_performance_summary()
        
        # Check summary structure
        self.assertIn("performance_metrics", summary)
        self.assertIn("current_context", summary)
        self.assertIn("tasks_completed", summary["performance_metrics"])
        self.assertIn("task_complexity", summary["current_context"])


def run_tests():
    """Run all enhanced agent tests."""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestEnhancedAgentOrchestrator,
        TestEnhancedToolDiscovery,
        TestSequentialThinkingTool,
        TestEnhancedResearchTool,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    print("🧪 Running Enhanced AI Agent Tests...")
    print("=" * 50)
    
    success = run_tests()
    
    if success:
        print("\n✅ All tests passed! Enhanced agent is ready for use.")
    else:
        print("\n❌ Some tests failed. Please review the output above.")
    
    print("=" * 50)
