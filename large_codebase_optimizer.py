"""
Large Codebase Optimization System for Enhanced AI Coding Agent.

This module provides advanced optimization capabilities for handling massive codebases:
- Intelligent indexing and caching mechanisms
- Parallel processing for file analysis
- Incremental analysis for code changes
- Memory-efficient processing for 100k+ files
- Smart chunking and batching strategies
- Performance monitoring and optimization
"""

import os
import json
import logging
import hashlib
import threading
import multiprocessing
import time
import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple, Generator, Set
from pathlib import Path
from dataclasses import dataclass, field, asdict
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from collections import defaultdict, deque
import pickle

from config import get_config

logger = logging.getLogger(__name__)


class OptimizationLevel(Enum):
    """Optimization levels for different codebase sizes."""
    SMALL = "small"      # < 1k files
    MEDIUM = "medium"    # 1k - 10k files
    LARGE = "large"      # 10k - 100k files
    MASSIVE = "massive"  # 100k+ files


class ProcessingStrategy(Enum):
    """Processing strategies for different scenarios."""
    SEQUENTIAL = "sequential"    # Single-threaded processing
    THREADED = "threaded"       # Multi-threaded processing
    PARALLEL = "parallel"       # Multi-process processing
    HYBRID = "hybrid"           # Combination of strategies


@dataclass
class CodebaseMetrics:
    """Metrics for codebase size and complexity."""
    total_files: int = 0
    total_lines: int = 0
    total_size_bytes: int = 0
    language_distribution: Dict[str, int] = field(default_factory=dict)
    directory_depth: int = 0
    largest_files: List[Tuple[str, int]] = field(default_factory=list)
    complexity_score: float = 0.0
    estimated_processing_time: float = 0.0


@dataclass
class OptimizationConfig:
    """Configuration for codebase optimization."""
    max_workers: int = 4
    chunk_size: int = 100
    cache_enabled: bool = True
    incremental_enabled: bool = True
    parallel_threshold: int = 1000
    memory_limit_mb: int = 1024
    batch_size: int = 50
    index_compression: bool = True


@dataclass
class ProcessingResult:
    """Result of file processing operation."""
    file_path: str
    success: bool
    processing_time: float
    file_size: int
    analysis_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class LargeCodebaseOptimizer:
    """
    Advanced optimizer for handling massive codebases efficiently.
    
    Features:
    - Intelligent file discovery and filtering
    - Parallel processing with optimal worker allocation
    - Incremental analysis with change detection
    - Memory-efficient caching and indexing
    - Performance monitoring and adaptive optimization
    - Smart batching and chunking strategies
    """
    
    def __init__(self, workspace_path: str, config: Optional[OptimizationConfig] = None):
        """
        Initialize the large codebase optimizer.
        
        Args:
            workspace_path: Path to the codebase root
            config: Optimization configuration
        """
        self.workspace_path = Path(workspace_path)
        self.config = config or OptimizationConfig()
        
        # Performance tracking
        self.metrics = CodebaseMetrics()
        self.processing_stats = {
            "files_processed": 0,
            "total_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0
        }
        
        # Caching system
        self.cache_dir = Path.home() / ".aiagent" / "codebase_cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Database for persistent indexing
        self.db_path = self.cache_dir / f"codebase_{self._get_workspace_hash()}.db"
        self._init_database()
        
        # Processing state
        self.file_hashes: Dict[str, str] = {}
        self.processing_queue = deque()
        self.results_cache: Dict[str, ProcessingResult] = {}
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Load existing cache
        self._load_cache()
    
    def _get_workspace_hash(self) -> str:
        """Get a hash for the workspace path."""
        return hashlib.md5(str(self.workspace_path).encode()).hexdigest()[:8]
    
    def _init_database(self) -> None:
        """Initialize the codebase database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS file_index (
                    file_path TEXT PRIMARY KEY,
                    file_hash TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    last_modified REAL NOT NULL,
                    analysis_data TEXT,
                    processing_time REAL,
                    created_at REAL DEFAULT (julianday('now')),
                    updated_at REAL DEFAULT (julianday('now'))
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS codebase_metrics (
                    id INTEGER PRIMARY KEY,
                    total_files INTEGER,
                    total_lines INTEGER,
                    total_size_bytes INTEGER,
                    language_distribution TEXT,
                    complexity_score REAL,
                    optimization_level TEXT,
                    created_at REAL DEFAULT (julianday('now'))
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_file_hash ON file_index(file_hash)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_last_modified ON file_index(last_modified)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_file_size ON file_index(file_size)")
    
    def analyze_codebase_size(self) -> CodebaseMetrics:
        """
        Analyze codebase size and determine optimization strategy.
        
        Returns:
            Codebase metrics and recommendations
        """
        logger.info(f"Analyzing codebase size: {self.workspace_path}")
        
        # Quick scan for metrics
        total_files = 0
        total_lines = 0
        total_size = 0
        language_dist = defaultdict(int)
        max_depth = 0
        largest_files = []
        
        for root, dirs, files in os.walk(self.workspace_path):
            # Skip hidden directories and common ignore patterns
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                'node_modules', '__pycache__', 'venv', 'env', 'build', 'dist', 'target'
            }]
            
            current_depth = len(Path(root).relative_to(self.workspace_path).parts)
            max_depth = max(max_depth, current_depth)
            
            for file in files:
                if file.startswith('.'):
                    continue
                
                file_path = Path(root) / file
                
                try:
                    stat = file_path.stat()
                    file_size = stat.st_size
                    
                    # Skip very large binary files
                    if file_size > 10 * 1024 * 1024:  # 10MB
                        continue
                    
                    total_files += 1
                    total_size += file_size
                    
                    # Language detection
                    ext = file_path.suffix.lower()
                    language_dist[ext] += 1
                    
                    # Track largest files
                    largest_files.append((str(file_path), file_size))
                    if len(largest_files) > 100:
                        largest_files.sort(key=lambda x: x[1], reverse=True)
                        largest_files = largest_files[:50]
                    
                    # Count lines for text files
                    if self._is_text_file(file_path):
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                total_lines += sum(1 for _ in f)
                        except:
                            pass
                
                except (OSError, PermissionError):
                    continue
        
        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(
            total_files, total_lines, max_depth, len(language_dist)
        )
        
        # Estimate processing time
        estimated_time = self._estimate_processing_time(total_files, total_lines)
        
        self.metrics = CodebaseMetrics(
            total_files=total_files,
            total_lines=total_lines,
            total_size_bytes=total_size,
            language_distribution=dict(language_dist),
            directory_depth=max_depth,
            largest_files=sorted(largest_files, key=lambda x: x[1], reverse=True)[:10],
            complexity_score=complexity_score,
            estimated_processing_time=estimated_time
        )
        
        logger.info(f"Codebase analysis complete: {total_files} files, {total_lines:,} lines")
        return self.metrics
    
    def get_optimization_strategy(self) -> Tuple[OptimizationLevel, ProcessingStrategy]:
        """
        Determine optimal processing strategy based on codebase size.
        
        Returns:
            Optimization level and processing strategy
        """
        if not self.metrics.total_files:
            self.analyze_codebase_size()
        
        total_files = self.metrics.total_files
        
        # Determine optimization level
        if total_files < 1000:
            opt_level = OptimizationLevel.SMALL
            strategy = ProcessingStrategy.SEQUENTIAL
        elif total_files < 10000:
            opt_level = OptimizationLevel.MEDIUM
            strategy = ProcessingStrategy.THREADED
        elif total_files < 100000:
            opt_level = OptimizationLevel.LARGE
            strategy = ProcessingStrategy.PARALLEL
        else:
            opt_level = OptimizationLevel.MASSIVE
            strategy = ProcessingStrategy.HYBRID
        
        # Adjust based on system resources
        cpu_count = multiprocessing.cpu_count()
        if strategy == ProcessingStrategy.PARALLEL and cpu_count < 4:
            strategy = ProcessingStrategy.THREADED
        
        logger.info(f"Optimization strategy: {opt_level.value} codebase, {strategy.value} processing")
        return opt_level, strategy
    
    def process_codebase_optimized(
        self,
        analysis_function: callable,
        incremental: bool = True,
        progress_callback: Optional[callable] = None
    ) -> Generator[ProcessingResult, None, None]:
        """
        Process codebase with optimal strategy.
        
        Args:
            analysis_function: Function to analyze each file
            incremental: Whether to use incremental processing
            progress_callback: Optional progress callback
        
        Yields:
            Processing results
        """
        opt_level, strategy = self.get_optimization_strategy()
        
        # Get files to process
        files_to_process = self._get_files_to_process(incremental)
        
        if not files_to_process:
            logger.info("No files need processing (incremental analysis)")
            return
        
        logger.info(f"Processing {len(files_to_process)} files with {strategy.value} strategy")
        
        # Process based on strategy
        if strategy == ProcessingStrategy.SEQUENTIAL:
            yield from self._process_sequential(files_to_process, analysis_function, progress_callback)
        elif strategy == ProcessingStrategy.THREADED:
            yield from self._process_threaded(files_to_process, analysis_function, progress_callback)
        elif strategy == ProcessingStrategy.PARALLEL:
            yield from self._process_parallel(files_to_process, analysis_function, progress_callback)
        else:  # HYBRID
            yield from self._process_hybrid(files_to_process, analysis_function, progress_callback)
    
    def _get_files_to_process(self, incremental: bool = True) -> List[str]:
        """Get list of files that need processing."""
        all_files = []
        
        for root, dirs, files in os.walk(self.workspace_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in {
                'node_modules', '__pycache__', 'venv', 'env', 'build', 'dist', 'target'
            }]
            
            for file in files:
                if file.startswith('.'):
                    continue
                
                file_path = Path(root) / file
                
                # Skip binary files and very large files
                if not self._is_text_file(file_path) or file_path.stat().st_size > 10 * 1024 * 1024:
                    continue
                
                all_files.append(str(file_path))
        
        if not incremental:
            return all_files
        
        # Filter for incremental processing
        files_to_process = []
        
        with sqlite3.connect(self.db_path) as conn:
            for file_path in all_files:
                try:
                    stat = Path(file_path).stat()
                    current_hash = self._get_file_hash(file_path)
                    
                    # Check if file needs processing
                    cursor = conn.execute(
                        "SELECT file_hash, last_modified FROM file_index WHERE file_path = ?",
                        (file_path,)
                    )
                    row = cursor.fetchone()
                    
                    if not row or row[0] != current_hash or row[1] != stat.st_mtime:
                        files_to_process.append(file_path)
                
                except (OSError, PermissionError):
                    continue
        
        return files_to_process
    
    def _process_sequential(
        self,
        files: List[str],
        analysis_function: callable,
        progress_callback: Optional[callable]
    ) -> Generator[ProcessingResult, None, None]:
        """Process files sequentially."""
        for i, file_path in enumerate(files):
            result = self._process_single_file(file_path, analysis_function)
            yield result
            
            if progress_callback:
                progress_callback(i + 1, len(files), result)
    
    def _process_threaded(
        self,
        files: List[str],
        analysis_function: callable,
        progress_callback: Optional[callable]
    ) -> Generator[ProcessingResult, None, None]:
        """Process files using thread pool."""
        max_workers = min(self.config.max_workers, len(files))
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self._process_single_file, file_path, analysis_function): file_path
                for file_path in files
            }
            
            completed = 0
            for future in as_completed(future_to_file):
                result = future.result()
                yield result
                
                completed += 1
                if progress_callback:
                    progress_callback(completed, len(files), result)
    
    def _process_parallel(
        self,
        files: List[str],
        analysis_function: callable,
        progress_callback: Optional[callable]
    ) -> Generator[ProcessingResult, None, None]:
        """Process files using process pool."""
        max_workers = min(multiprocessing.cpu_count(), len(files) // 10 + 1)
        
        # Split files into chunks for better load balancing
        chunk_size = max(1, len(files) // (max_workers * 4))
        file_chunks = [files[i:i + chunk_size] for i in range(0, len(files), chunk_size)]
        
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit chunk processing tasks
            future_to_chunk = {
                executor.submit(self._process_file_chunk, chunk, analysis_function): chunk
                for chunk in file_chunks
            }
            
            completed = 0
            for future in as_completed(future_to_chunk):
                chunk_results = future.result()
                for result in chunk_results:
                    yield result
                    completed += 1
                    
                    if progress_callback:
                        progress_callback(completed, len(files), result)
    
    def _process_hybrid(
        self,
        files: List[str],
        analysis_function: callable,
        progress_callback: Optional[callable]
    ) -> Generator[ProcessingResult, None, None]:
        """Process files using hybrid strategy."""
        # For massive codebases, use a combination of strategies
        # Process large files in parallel, small files in batches
        
        large_files = []
        small_files = []
        
        for file_path in files:
            try:
                size = Path(file_path).stat().st_size
                if size > 100 * 1024:  # 100KB threshold
                    large_files.append(file_path)
                else:
                    small_files.append(file_path)
            except:
                small_files.append(file_path)
        
        # Process large files in parallel
        if large_files:
            logger.info(f"Processing {len(large_files)} large files in parallel")
            yield from self._process_parallel(large_files, analysis_function, progress_callback)
        
        # Process small files in threaded batches
        if small_files:
            logger.info(f"Processing {len(small_files)} small files in threaded batches")
            batch_size = self.config.batch_size
            for i in range(0, len(small_files), batch_size):
                batch = small_files[i:i + batch_size]
                yield from self._process_threaded(batch, analysis_function, progress_callback)
    
    def _process_single_file(self, file_path: str, analysis_function: callable) -> ProcessingResult:
        """Process a single file."""
        start_time = time.time()
        
        try:
            # Check cache first
            if self.config.cache_enabled:
                cached_result = self._get_cached_result(file_path)
                if cached_result:
                    self.processing_stats["cache_hits"] += 1
                    return cached_result
            
            self.processing_stats["cache_misses"] += 1
            
            # Get file info
            file_stat = Path(file_path).stat()
            file_size = file_stat.st_size
            
            # Analyze file
            analysis_data = analysis_function(file_path)
            
            processing_time = time.time() - start_time
            
            result = ProcessingResult(
                file_path=file_path,
                success=True,
                processing_time=processing_time,
                file_size=file_size,
                analysis_data=analysis_data
            )
            
            # Cache result
            if self.config.cache_enabled:
                self._cache_result(result)
            
            self.processing_stats["files_processed"] += 1
            self.processing_stats["total_processing_time"] += processing_time
            
            return result
        
        except Exception as e:
            processing_time = time.time() - start_time
            self.processing_stats["errors"] += 1
            
            return ProcessingResult(
                file_path=file_path,
                success=False,
                processing_time=processing_time,
                file_size=0,
                error_message=str(e)
            )
    
    def _process_file_chunk(self, file_chunk: List[str], analysis_function: callable) -> List[ProcessingResult]:
        """Process a chunk of files (for multiprocessing)."""
        results = []
        for file_path in file_chunk:
            result = self._process_single_file(file_path, analysis_function)
            results.append(result)
        return results

    def _get_cached_result(self, file_path: str) -> Optional[ProcessingResult]:
        """Get cached result for a file."""
        try:
            file_stat = Path(file_path).stat()
            current_hash = self._get_file_hash(file_path)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT file_hash, analysis_data, processing_time FROM file_index WHERE file_path = ?",
                    (file_path,)
                )
                row = cursor.fetchone()

                if row and row[0] == current_hash:
                    analysis_data = json.loads(row[1]) if row[1] else None
                    return ProcessingResult(
                        file_path=file_path,
                        success=True,
                        processing_time=row[2] or 0.0,
                        file_size=file_stat.st_size,
                        analysis_data=analysis_data
                    )

        except Exception as e:
            logger.debug(f"Cache lookup failed for {file_path}: {e}")

        return None

    def _cache_result(self, result: ProcessingResult) -> None:
        """Cache a processing result."""
        try:
            file_stat = Path(result.file_path).stat()
            file_hash = self._get_file_hash(result.file_path)
            analysis_json = json.dumps(result.analysis_data) if result.analysis_data else None

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO file_index
                    (file_path, file_hash, file_size, last_modified, analysis_data, processing_time, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, julianday('now'))
                """, (
                    result.file_path, file_hash, result.file_size,
                    file_stat.st_mtime, analysis_json, result.processing_time
                ))

        except Exception as e:
            logger.warning(f"Failed to cache result for {result.file_path}: {e}")

    def _get_file_hash(self, file_path: str) -> str:
        """Get hash of file content."""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is a text file."""
        # Common text file extensions
        text_extensions = {
            '.py', '.js', '.ts', '.tsx', '.jsx', '.java', '.c', '.cpp', '.h', '.hpp',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala', '.r',
            '.html', '.htm', '.css', '.scss', '.sass', '.less', '.xml', '.json',
            '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf', '.md', '.txt',
            '.sql', '.sh', '.bash', '.zsh', '.fish', '.ps1', '.bat', '.cmd'
        }

        if file_path.suffix.lower() in text_extensions:
            return True

        # Check for binary content
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                return b'\0' not in chunk
        except:
            return False

    def _calculate_complexity_score(
        self,
        total_files: int,
        total_lines: int,
        max_depth: int,
        language_count: int
    ) -> float:
        """Calculate complexity score for the codebase."""
        # Normalize factors
        file_factor = min(total_files / 10000, 1.0)  # 0-1 based on file count
        line_factor = min(total_lines / 1000000, 1.0)  # 0-1 based on line count
        depth_factor = min(max_depth / 20, 1.0)  # 0-1 based on directory depth
        lang_factor = min(language_count / 10, 1.0)  # 0-1 based on language diversity

        # Weighted complexity score
        complexity = (
            file_factor * 0.4 +
            line_factor * 0.3 +
            depth_factor * 0.2 +
            lang_factor * 0.1
        )

        return complexity

    def _estimate_processing_time(self, total_files: int, total_lines: int) -> float:
        """Estimate processing time in seconds."""
        # Base estimates (files per second, lines per second)
        files_per_second = 50
        lines_per_second = 10000

        file_time = total_files / files_per_second
        line_time = total_lines / lines_per_second

        # Use the larger estimate with some overhead
        estimated_time = max(file_time, line_time) * 1.5

        return estimated_time

    def _load_cache(self) -> None:
        """Load existing cache data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT file_path, file_hash FROM file_index")
                for row in cursor:
                    self.file_hashes[row[0]] = row[1]
        except Exception as e:
            logger.debug(f"Failed to load cache: {e}")

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            **self.processing_stats,
            "cache_hit_rate": (
                self.processing_stats["cache_hits"] /
                max(1, self.processing_stats["cache_hits"] + self.processing_stats["cache_misses"])
            ),
            "average_processing_time": (
                self.processing_stats["total_processing_time"] /
                max(1, self.processing_stats["files_processed"])
            ),
            "codebase_metrics": asdict(self.metrics)
        }

    def clear_cache(self) -> None:
        """Clear all cached data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("DELETE FROM file_index")
                conn.execute("DELETE FROM codebase_metrics")

            self.file_hashes.clear()
            self.results_cache.clear()

            logger.info("Cache cleared successfully")

        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")

    def optimize_for_memory(self) -> None:
        """Optimize memory usage for large codebases."""
        # Clear in-memory caches if memory usage is high
        import psutil

        try:
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024

            if memory_mb > self.config.memory_limit_mb:
                logger.info(f"Memory usage ({memory_mb:.1f}MB) exceeds limit, optimizing...")

                # Clear results cache
                self.results_cache.clear()

                # Reduce chunk size
                self.config.chunk_size = max(10, self.config.chunk_size // 2)

                # Force garbage collection
                import gc
                gc.collect()

                logger.info("Memory optimization complete")

        except ImportError:
            logger.debug("psutil not available for memory monitoring")
        except Exception as e:
            logger.warning(f"Memory optimization failed: {e}")


# Utility functions for integration with existing tools

def create_optimized_analyzer(workspace_path: str) -> LargeCodebaseOptimizer:
    """Create an optimized analyzer for the given workspace."""
    return LargeCodebaseOptimizer(workspace_path)


def analyze_file_optimized(file_path: str) -> Dict[str, Any]:
    """Optimized file analysis function."""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Basic analysis
        lines = content.split('\n')
        analysis = {
            "file_path": file_path,
            "line_count": len(lines),
            "character_count": len(content),
            "language": Path(file_path).suffix.lower(),
            "functions": [],
            "classes": [],
            "imports": []
        }

        # Language-specific analysis
        if file_path.endswith('.py'):
            analysis.update(_analyze_python_optimized(content))
        elif file_path.endswith(('.js', '.ts', '.jsx', '.tsx')):
            analysis.update(_analyze_javascript_optimized(content))

        return analysis

    except Exception as e:
        return {"error": str(e), "file_path": file_path}


def _analyze_python_optimized(content: str) -> Dict[str, Any]:
    """Optimized Python file analysis."""
    import ast

    try:
        tree = ast.parse(content)

        functions = []
        classes = []
        imports = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append({
                    "name": node.name,
                    "line": node.lineno,
                    "args": len(node.args.args)
                })
            elif isinstance(node, ast.ClassDef):
                classes.append({
                    "name": node.name,
                    "line": node.lineno,
                    "methods": len([n for n in node.body if isinstance(n, ast.FunctionDef)])
                })
            elif isinstance(node, (ast.Import, ast.ImportFrom)):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                else:
                    imports.append(node.module or "")

        return {
            "functions": functions,
            "classes": classes,
            "imports": list(set(imports))
        }

    except:
        return {"functions": [], "classes": [], "imports": []}


def _analyze_javascript_optimized(content: str) -> Dict[str, Any]:
    """Optimized JavaScript/TypeScript file analysis."""
    import re

    # Simple regex-based analysis for performance
    functions = []
    classes = []
    imports = []

    # Find function declarations
    func_pattern = r'(?:function\s+(\w+)|(\w+)\s*:\s*function|(\w+)\s*=\s*function|(\w+)\s*=>\s*)'
    for match in re.finditer(func_pattern, content):
        name = next(g for g in match.groups() if g)
        if name:
            functions.append({"name": name, "line": content[:match.start()].count('\n') + 1})

    # Find class declarations
    class_pattern = r'class\s+(\w+)'
    for match in re.finditer(class_pattern, content):
        classes.append({
            "name": match.group(1),
            "line": content[:match.start()].count('\n') + 1
        })

    # Find imports
    import_pattern = r'import\s+.*?from\s+[\'"]([^\'"]+)[\'"]'
    for match in re.finditer(import_pattern, content):
        imports.append(match.group(1))

    return {
        "functions": functions,
        "classes": classes,
        "imports": list(set(imports))
    }
