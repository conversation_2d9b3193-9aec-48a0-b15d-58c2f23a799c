"""
Intelligence Engine for Enhanced AI Coding Agent.

This module provides advanced intelligence and performance improvements including:
- Context-aware code suggestions based on project history
- Advanced project architecture analysis and recommendations
- Intelligent pattern recognition and learning
- Performance optimization suggestions
- Cross-project knowledge transfer
"""

import json
import logging
import sqlite3
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple, Set
from pathlib import Path
from dataclasses import dataclass, field, asdict
from enum import Enum
from collections import defaultdict, Counter
import pickle

from config import get_config

logger = logging.getLogger(__name__)


class IntelligenceLevel(Enum):
    """Intelligence levels for different capabilities."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class SuggestionCategory(Enum):
    """Categories of intelligent suggestions."""
    ARCHITECTURE = "architecture"
    PERFORMANCE = "performance"
    SECURITY = "security"
    MAINTAINABILITY = "maintainability"
    BEST_PRACTICES = "best_practices"
    OPTIMIZATION = "optimization"


@dataclass
class IntelligentSuggestion:
    """Intelligent suggestion with context awareness."""
    category: SuggestionCategory
    title: str
    description: str
    reasoning: str
    confidence: float  # 0.0 to 1.0
    impact_score: float  # 0.0 to 1.0
    implementation_effort: str  # "low", "medium", "high"
    
    # Context information
    project_context: Dict[str, Any] = field(default_factory=dict)
    historical_data: Dict[str, Any] = field(default_factory=dict)
    related_patterns: List[str] = field(default_factory=list)
    
    # Implementation details
    code_examples: List[str] = field(default_factory=list)
    resources: List[str] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)


@dataclass
class ProjectInsight:
    """Project-level insight and analysis."""
    insight_type: str
    title: str
    description: str
    metrics: Dict[str, Any] = field(default_factory=dict)
    trends: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)
    confidence: float = 0.8


class IntelligenceEngine:
    """
    Advanced intelligence engine for context-aware suggestions and analysis.
    
    Features:
    - Context-aware code suggestions based on project history
    - Advanced project architecture analysis
    - Intelligent pattern recognition and learning
    - Performance optimization recommendations
    - Cross-project knowledge transfer
    - Adaptive learning from user feedback
    """
    
    def __init__(self, workspace_path: Optional[str] = None):
        """
        Initialize the intelligence engine.
        
        Args:
            workspace_path: Current workspace path for context
        """
        self.config = get_config()
        self.workspace_path = workspace_path
        
        # Intelligence database
        self.db_path = self.config.data_dir / "intelligence_engine.db"
        self.config.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Pattern recognition
        self.known_patterns = self._load_known_patterns()
        self.architecture_patterns = self._load_architecture_patterns()
        
        # Learning and adaptation
        self.learning_data = {
            "user_preferences": {},
            "successful_suggestions": [],
            "rejected_suggestions": [],
            "project_patterns": {},
            "performance_metrics": {}
        }
        
        # Context tracking
        self.current_context = {
            "project_type": None,
            "languages": set(),
            "frameworks": set(),
            "architecture_style": None,
            "team_size": "unknown",
            "project_maturity": "unknown"
        }
        
        # Performance tracking
        self.performance_metrics = {
            "suggestions_generated": 0,
            "suggestions_accepted": 0,
            "analysis_accuracy": 0.0,
            "learning_iterations": 0
        }
        
        # Load existing data
        self._load_learning_data()
        self._analyze_current_context()
    
    def _init_database(self) -> None:
        """Initialize the intelligence database."""
        with sqlite3.connect(self.db_path) as conn:
            # Project insights table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS project_insights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_path TEXT NOT NULL,
                    insight_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    metrics TEXT,
                    trends TEXT,
                    recommendations TEXT,
                    confidence REAL DEFAULT 0.8,
                    created_at REAL DEFAULT (julianday('now')),
                    updated_at REAL DEFAULT (julianday('now'))
                )
            """)
            
            # Suggestions history table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS suggestions_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_path TEXT NOT NULL,
                    category TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    impact_score REAL NOT NULL,
                    accepted BOOLEAN DEFAULT NULL,
                    feedback TEXT,
                    context_data TEXT,
                    created_at REAL DEFAULT (julianday('now'))
                )
            """)
            
            # Learning patterns table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    pattern_type TEXT NOT NULL,
                    pattern_data TEXT NOT NULL,
                    success_rate REAL DEFAULT 0.0,
                    usage_count INTEGER DEFAULT 0,
                    last_used REAL DEFAULT (julianday('now'))
                )
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_project_path ON project_insights(project_path)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_suggestion_category ON suggestions_history(category)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_pattern_type ON learning_patterns(pattern_type)")
    
    def analyze_project_intelligence(self, project_path: str) -> List[ProjectInsight]:
        """
        Perform intelligent project analysis with context awareness.
        
        Args:
            project_path: Path to the project root
        
        Returns:
            List of project insights and recommendations
        """
        insights = []
        
        try:
            # Update context
            self._analyze_project_context(project_path)
            
            # Architecture analysis
            arch_insight = self._analyze_architecture_intelligence(project_path)
            if arch_insight:
                insights.append(arch_insight)
            
            # Performance analysis
            perf_insight = self._analyze_performance_patterns(project_path)
            if perf_insight:
                insights.append(perf_insight)
            
            # Security analysis
            security_insight = self._analyze_security_patterns(project_path)
            if security_insight:
                insights.append(security_insight)
            
            # Maintainability analysis
            maint_insight = self._analyze_maintainability_patterns(project_path)
            if maint_insight:
                insights.append(maint_insight)
            
            # Store insights
            self._store_project_insights(project_path, insights)
            
        except Exception as e:
            logger.error(f"Project intelligence analysis failed: {e}")
        
        return insights
    
    def generate_context_aware_suggestions(
        self,
        code_context: str,
        file_path: str,
        project_context: Optional[Dict[str, Any]] = None
    ) -> List[IntelligentSuggestion]:
        """
        Generate context-aware suggestions based on project history and patterns.
        
        Args:
            code_context: Current code context
            file_path: File being edited
            project_context: Additional project context
        
        Returns:
            List of intelligent suggestions
        """
        suggestions = []
        
        try:
            # Analyze current context
            context = self._analyze_code_context(code_context, file_path, project_context)
            
            # Generate suggestions based on patterns
            pattern_suggestions = self._suggest_from_patterns(context)
            suggestions.extend(pattern_suggestions)
            
            # Generate suggestions based on history
            history_suggestions = self._suggest_from_history(context)
            suggestions.extend(history_suggestions)
            
            # Generate suggestions based on best practices
            practice_suggestions = self._suggest_best_practices(context)
            suggestions.extend(practice_suggestions)
            
            # Rank suggestions by relevance and confidence
            suggestions = self._rank_suggestions(suggestions, context)
            
            # Store suggestions for learning
            self._store_suggestions_for_learning(suggestions, context)
            
            self.performance_metrics["suggestions_generated"] += len(suggestions)
            
        except Exception as e:
            logger.error(f"Context-aware suggestion generation failed: {e}")
        
        return suggestions[:10]  # Return top 10 suggestions
    
    def learn_from_feedback(
        self,
        suggestion_id: str,
        accepted: bool,
        feedback: Optional[str] = None
    ) -> None:
        """
        Learn from user feedback to improve future suggestions.
        
        Args:
            suggestion_id: ID of the suggestion
            accepted: Whether the suggestion was accepted
            feedback: Optional user feedback
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Update suggestion with feedback
                conn.execute("""
                    UPDATE suggestions_history 
                    SET accepted = ?, feedback = ?, updated_at = julianday('now')
                    WHERE id = ?
                """, (accepted, feedback, suggestion_id))
                
                # Update learning patterns
                if accepted:
                    self.performance_metrics["suggestions_accepted"] += 1
                    self._update_successful_patterns(suggestion_id)
                else:
                    self._update_rejected_patterns(suggestion_id)
                
                # Adapt future suggestions
                self._adapt_suggestion_algorithms()
                
        except Exception as e:
            logger.error(f"Learning from feedback failed: {e}")
    
    def get_performance_recommendations(self, project_path: str) -> List[IntelligentSuggestion]:
        """
        Get performance optimization recommendations for the project.
        
        Args:
            project_path: Path to the project root
        
        Returns:
            List of performance recommendations
        """
        recommendations = []
        
        try:
            # Analyze performance patterns
            perf_data = self._analyze_performance_data(project_path)
            
            # Generate recommendations based on analysis
            if perf_data.get("large_files_count", 0) > 10:
                recommendations.append(IntelligentSuggestion(
                    category=SuggestionCategory.PERFORMANCE,
                    title="Optimize Large Files",
                    description="Consider breaking down large files for better performance",
                    reasoning="Large files can slow down IDE performance and compilation",
                    confidence=0.8,
                    impact_score=0.7,
                    implementation_effort="medium",
                    code_examples=["# Split large classes into smaller modules"],
                    tags=["performance", "maintainability"]
                ))
            
            if perf_data.get("deep_nesting", 0) > 5:
                recommendations.append(IntelligentSuggestion(
                    category=SuggestionCategory.PERFORMANCE,
                    title="Reduce Directory Nesting",
                    description="Deep directory structures can impact performance",
                    reasoning="Excessive nesting makes navigation and builds slower",
                    confidence=0.7,
                    impact_score=0.6,
                    implementation_effort="low",
                    tags=["performance", "organization"]
                ))
            
        except Exception as e:
            logger.error(f"Performance recommendations failed: {e}")
        
        return recommendations
    
    def _load_known_patterns(self) -> Dict[str, Any]:
        """Load known code patterns and anti-patterns."""
        return {
            "python": {
                "good_patterns": [
                    {"name": "list_comprehension", "confidence": 0.9},
                    {"name": "context_manager", "confidence": 0.8},
                    {"name": "generator_expression", "confidence": 0.7}
                ],
                "anti_patterns": [
                    {"name": "global_variables", "confidence": 0.9},
                    {"name": "bare_except", "confidence": 0.8},
                    {"name": "mutable_default_args", "confidence": 0.9}
                ]
            },
            "javascript": {
                "good_patterns": [
                    {"name": "arrow_functions", "confidence": 0.8},
                    {"name": "destructuring", "confidence": 0.7},
                    {"name": "async_await", "confidence": 0.9}
                ],
                "anti_patterns": [
                    {"name": "var_usage", "confidence": 0.9},
                    {"name": "callback_hell", "confidence": 0.8},
                    {"name": "global_pollution", "confidence": 0.9}
                ]
            }
        }
    
    def _load_architecture_patterns(self) -> Dict[str, Any]:
        """Load architecture patterns and their characteristics."""
        return {
            "mvc": {
                "indicators": ["model", "view", "controller"],
                "benefits": ["separation_of_concerns", "testability"],
                "drawbacks": ["complexity_for_small_apps"]
            },
            "microservices": {
                "indicators": ["service", "api", "gateway"],
                "benefits": ["scalability", "independence"],
                "drawbacks": ["complexity", "network_overhead"]
            },
            "layered": {
                "indicators": ["controller", "service", "repository"],
                "benefits": ["clear_separation", "maintainability"],
                "drawbacks": ["performance_overhead"]
            }
        }
    
    def _analyze_current_context(self) -> None:
        """Analyze current project context."""
        if not self.workspace_path:
            return
        
        try:
            workspace = Path(self.workspace_path)
            
            # Detect languages
            for file_path in workspace.rglob("*"):
                if file_path.is_file():
                    suffix = file_path.suffix.lower()
                    if suffix == ".py":
                        self.current_context["languages"].add("python")
                    elif suffix in [".js", ".jsx"]:
                        self.current_context["languages"].add("javascript")
                    elif suffix in [".ts", ".tsx"]:
                        self.current_context["languages"].add("typescript")
                    elif suffix == ".java":
                        self.current_context["languages"].add("java")
            
            # Detect frameworks
            if (workspace / "package.json").exists():
                try:
                    with open(workspace / "package.json") as f:
                        package_data = json.load(f)
                        deps = {**package_data.get("dependencies", {}), 
                               **package_data.get("devDependencies", {})}
                        
                        if "react" in deps:
                            self.current_context["frameworks"].add("react")
                        if "vue" in deps:
                            self.current_context["frameworks"].add("vue")
                        if "angular" in deps:
                            self.current_context["frameworks"].add("angular")
                except:
                    pass
            
            if (workspace / "requirements.txt").exists() or (workspace / "pyproject.toml").exists():
                self.current_context["frameworks"].add("python")
                
                # Check for specific Python frameworks
                for req_file in ["requirements.txt", "pyproject.toml"]:
                    req_path = workspace / req_file
                    if req_path.exists():
                        try:
                            content = req_path.read_text().lower()
                            if "django" in content:
                                self.current_context["frameworks"].add("django")
                            if "flask" in content:
                                self.current_context["frameworks"].add("flask")
                            if "fastapi" in content:
                                self.current_context["frameworks"].add("fastapi")
                        except:
                            pass
            
        except Exception as e:
            logger.debug(f"Context analysis failed: {e}")
    
    def _analyze_project_context(self, project_path: str) -> None:
        """Analyze project-specific context."""
        try:
            project_dir = Path(project_path)
            
            # Count files and estimate project size
            file_count = len(list(project_dir.rglob("*.py"))) + len(list(project_dir.rglob("*.js")))
            
            if file_count < 10:
                self.current_context["project_maturity"] = "small"
            elif file_count < 100:
                self.current_context["project_maturity"] = "medium"
            else:
                self.current_context["project_maturity"] = "large"
            
            # Detect project type
            if (project_dir / "manage.py").exists():
                self.current_context["project_type"] = "django_web_app"
            elif (project_dir / "app.py").exists() or (project_dir / "main.py").exists():
                self.current_context["project_type"] = "python_application"
            elif (project_dir / "package.json").exists():
                self.current_context["project_type"] = "node_application"
            
        except Exception as e:
            logger.debug(f"Project context analysis failed: {e}")

    def _analyze_architecture_intelligence(self, project_path: str) -> Optional[ProjectInsight]:
        """Analyze project architecture with intelligence."""
        try:
            project_dir = Path(project_path)

            # Scan directory structure
            directories = set()
            for item in project_dir.rglob("*"):
                if item.is_dir():
                    directories.add(item.name.lower())

            # Detect architecture patterns
            detected_patterns = []
            for pattern_name, pattern_data in self.architecture_patterns.items():
                indicators = pattern_data["indicators"]
                if any(indicator in directories for indicator in indicators):
                    detected_patterns.append(pattern_name)

            # Generate recommendations
            recommendations = []
            if not detected_patterns:
                recommendations.append("Consider adopting a well-known architecture pattern")
            elif len(detected_patterns) > 2:
                recommendations.append("Multiple architecture patterns detected - consider consolidating")

            return ProjectInsight(
                insight_type="architecture",
                title="Architecture Analysis",
                description=f"Detected patterns: {', '.join(detected_patterns) if detected_patterns else 'None'}",
                metrics={"patterns_count": len(detected_patterns), "directories_count": len(directories)},
                recommendations=recommendations,
                confidence=0.8
            )

        except Exception as e:
            logger.error(f"Architecture intelligence analysis failed: {e}")
            return None

    def _analyze_performance_patterns(self, project_path: str) -> Optional[ProjectInsight]:
        """Analyze performance patterns in the project."""
        try:
            project_dir = Path(project_path)

            # Analyze file sizes and counts
            large_files = []
            total_files = 0
            total_size = 0

            for file_path in project_dir.rglob("*"):
                if file_path.is_file() and file_path.suffix in ['.py', '.js', '.ts', '.java']:
                    total_files += 1
                    size = file_path.stat().st_size
                    total_size += size

                    if size > 100 * 1024:  # Files larger than 100KB
                        large_files.append(str(file_path))

            # Generate recommendations
            recommendations = []
            if len(large_files) > 5:
                recommendations.append("Consider breaking down large files for better performance")
            if total_files > 1000:
                recommendations.append("Large project - consider modularization strategies")

            return ProjectInsight(
                insight_type="performance",
                title="Performance Analysis",
                description=f"Found {len(large_files)} large files in {total_files} total files",
                metrics={
                    "total_files": total_files,
                    "large_files_count": len(large_files),
                    "average_file_size": total_size / max(1, total_files)
                },
                recommendations=recommendations,
                confidence=0.7
            )

        except Exception as e:
            logger.error(f"Performance pattern analysis failed: {e}")
            return None

    def _analyze_security_patterns(self, project_path: str) -> Optional[ProjectInsight]:
        """Analyze security patterns in the project."""
        try:
            project_dir = Path(project_path)

            # Look for common security files and patterns
            security_files = []
            potential_issues = []

            for file_path in project_dir.rglob("*"):
                if file_path.is_file():
                    name = file_path.name.lower()

                    # Security-related files
                    if any(sec in name for sec in ['security', 'auth', 'login', 'password']):
                        security_files.append(str(file_path))

                    # Potential security issues
                    if name in ['config.py', 'settings.py', '.env']:
                        try:
                            content = file_path.read_text(errors='ignore')
                            if any(word in content.lower() for word in ['password', 'secret', 'key']):
                                potential_issues.append(f"Potential secrets in {file_path.name}")
                        except:
                            pass

            recommendations = []
            if potential_issues:
                recommendations.append("Review files for hardcoded secrets")
            if not security_files:
                recommendations.append("Consider implementing security measures")

            return ProjectInsight(
                insight_type="security",
                title="Security Analysis",
                description=f"Found {len(security_files)} security-related files",
                metrics={
                    "security_files_count": len(security_files),
                    "potential_issues_count": len(potential_issues)
                },
                recommendations=recommendations,
                confidence=0.6
            )

        except Exception as e:
            logger.error(f"Security pattern analysis failed: {e}")
            return None

    def _analyze_maintainability_patterns(self, project_path: str) -> Optional[ProjectInsight]:
        """Analyze maintainability patterns in the project."""
        try:
            project_dir = Path(project_path)

            # Look for maintainability indicators
            test_files = list(project_dir.rglob("*test*.py")) + list(project_dir.rglob("*test*.js"))
            doc_files = list(project_dir.rglob("*.md")) + list(project_dir.rglob("*.rst"))
            config_files = list(project_dir.rglob("*.json")) + list(project_dir.rglob("*.yaml"))

            # Calculate maintainability score
            score = 0.0
            if test_files:
                score += 0.3
            if doc_files:
                score += 0.2
            if config_files:
                score += 0.1

            recommendations = []
            if not test_files:
                recommendations.append("Add comprehensive test coverage")
            if not doc_files:
                recommendations.append("Add project documentation")
            if len(config_files) > 10:
                recommendations.append("Consider consolidating configuration files")

            return ProjectInsight(
                insight_type="maintainability",
                title="Maintainability Analysis",
                description=f"Maintainability score: {score:.1%}",
                metrics={
                    "test_files_count": len(test_files),
                    "doc_files_count": len(doc_files),
                    "config_files_count": len(config_files),
                    "maintainability_score": score
                },
                recommendations=recommendations,
                confidence=0.8
            )

        except Exception as e:
            logger.error(f"Maintainability pattern analysis failed: {e}")
            return None

    def _store_project_insights(self, project_path: str, insights: List[ProjectInsight]) -> None:
        """Store project insights in the database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for insight in insights:
                    conn.execute("""
                        INSERT OR REPLACE INTO project_insights
                        (project_path, insight_type, title, description, metrics,
                         trends, recommendations, confidence, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, julianday('now'))
                    """, (
                        project_path, insight.insight_type, insight.title,
                        insight.description, json.dumps(insight.metrics),
                        json.dumps(insight.trends), json.dumps(insight.recommendations),
                        insight.confidence
                    ))

        except Exception as e:
            logger.error(f"Failed to store project insights: {e}")

    def _analyze_code_context(
        self,
        code_context: str,
        file_path: str,
        project_context: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze code context for intelligent suggestions."""
        context = {
            "file_path": file_path,
            "file_extension": Path(file_path).suffix.lower(),
            "code_length": len(code_context),
            "line_count": len(code_context.split('\n')),
            "project_context": project_context or {},
            "current_context": self.current_context.copy()
        }

        # Analyze code patterns
        if context["file_extension"] == ".py":
            context["language"] = "python"
            context["imports"] = self._extract_python_imports(code_context)
            context["functions"] = self._count_python_functions(code_context)
            context["classes"] = self._count_python_classes(code_context)
        elif context["file_extension"] in [".js", ".jsx", ".ts", ".tsx"]:
            context["language"] = "javascript"
            context["imports"] = self._extract_js_imports(code_context)
            context["functions"] = self._count_js_functions(code_context)

        return context

    def _suggest_from_patterns(self, context: Dict[str, Any]) -> List[IntelligentSuggestion]:
        """Generate suggestions based on known patterns."""
        suggestions = []

        language = context.get("language", "unknown")
        patterns = self.known_patterns.get(language, {})

        # Check for anti-patterns
        for anti_pattern in patterns.get("anti_patterns", []):
            if self._detect_pattern_in_context(anti_pattern["name"], context):
                suggestions.append(IntelligentSuggestion(
                    category=SuggestionCategory.BEST_PRACTICES,
                    title=f"Avoid {anti_pattern['name'].replace('_', ' ').title()}",
                    description=f"Detected {anti_pattern['name']} anti-pattern",
                    reasoning="This pattern can lead to maintenance issues",
                    confidence=anti_pattern["confidence"],
                    impact_score=0.7,
                    implementation_effort="low",
                    tags=["anti-pattern", "best-practices"]
                ))

        return suggestions

    def _suggest_from_history(self, context: Dict[str, Any]) -> List[IntelligentSuggestion]:
        """Generate suggestions based on historical data."""
        suggestions = []

        try:
            with sqlite3.connect(self.db_path) as conn:
                # Find similar contexts from history
                cursor = conn.execute("""
                    SELECT category, title, description, confidence, impact_score
                    FROM suggestions_history
                    WHERE accepted = 1 AND project_path = ?
                    ORDER BY created_at DESC LIMIT 5
                """, (self.workspace_path or "",))

                for row in cursor:
                    suggestions.append(IntelligentSuggestion(
                        category=SuggestionCategory(row[0]),
                        title=f"Similar to previous: {row[1]}",
                        description=row[2],
                        reasoning="Based on previously accepted suggestions",
                        confidence=row[3] * 0.8,  # Slightly lower confidence
                        impact_score=row[4],
                        implementation_effort="medium",
                        tags=["historical", "proven"]
                    ))

        except Exception as e:
            logger.debug(f"History-based suggestions failed: {e}")

        return suggestions

    def _suggest_best_practices(self, context: Dict[str, Any]) -> List[IntelligentSuggestion]:
        """Generate suggestions based on best practices."""
        suggestions = []

        language = context.get("language", "unknown")

        # Language-specific best practices
        if language == "python":
            if context.get("line_count", 0) > 500:
                suggestions.append(IntelligentSuggestion(
                    category=SuggestionCategory.MAINTAINABILITY,
                    title="Consider Breaking Down Large File",
                    description="Large files can be harder to maintain and navigate",
                    reasoning="Files with many lines of code are harder to understand",
                    confidence=0.7,
                    impact_score=0.6,
                    implementation_effort="medium",
                    tags=["maintainability", "organization"]
                ))

        return suggestions

    def _rank_suggestions(
        self,
        suggestions: List[IntelligentSuggestion],
        context: Dict[str, Any]
    ) -> List[IntelligentSuggestion]:
        """Rank suggestions by relevance and confidence."""
        def score_suggestion(suggestion: IntelligentSuggestion) -> float:
            score = suggestion.confidence * 0.4 + suggestion.impact_score * 0.3

            # Boost score based on context relevance
            if suggestion.category == SuggestionCategory.PERFORMANCE:
                if context.get("line_count", 0) > 1000:
                    score += 0.2

            # Boost based on project maturity
            if self.current_context.get("project_maturity") == "large":
                if suggestion.category in [SuggestionCategory.ARCHITECTURE, SuggestionCategory.MAINTAINABILITY]:
                    score += 0.1

            return score

        suggestions.sort(key=score_suggestion, reverse=True)
        return suggestions

    def _store_suggestions_for_learning(
        self,
        suggestions: List[IntelligentSuggestion],
        context: Dict[str, Any]
    ) -> None:
        """Store suggestions for learning purposes."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                for suggestion in suggestions:
                    conn.execute("""
                        INSERT INTO suggestions_history
                        (project_path, category, title, description, confidence,
                         impact_score, context_data)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        self.workspace_path or "",
                        suggestion.category.value,
                        suggestion.title,
                        suggestion.description,
                        suggestion.confidence,
                        suggestion.impact_score,
                        json.dumps(context)
                    ))

        except Exception as e:
            logger.error(f"Failed to store suggestions for learning: {e}")

    def _extract_python_imports(self, code: str) -> List[str]:
        """Extract Python imports from code."""
        imports = []
        for line in code.split('\n'):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                imports.append(line)
        return imports

    def _count_python_functions(self, code: str) -> int:
        """Count Python functions in code."""
        return len([line for line in code.split('\n') if line.strip().startswith('def ')])

    def _count_python_classes(self, code: str) -> int:
        """Count Python classes in code."""
        return len([line for line in code.split('\n') if line.strip().startswith('class ')])

    def _extract_js_imports(self, code: str) -> List[str]:
        """Extract JavaScript imports from code."""
        imports = []
        for line in code.split('\n'):
            line = line.strip()
            if line.startswith('import ') or line.startswith('const ') and 'require(' in line:
                imports.append(line)
        return imports

    def _count_js_functions(self, code: str) -> int:
        """Count JavaScript functions in code."""
        import re
        return len(re.findall(r'function\s+\w+|=>\s*{|\w+\s*:\s*function', code))

    def _detect_pattern_in_context(self, pattern_name: str, context: Dict[str, Any]) -> bool:
        """Detect if a pattern exists in the given context."""
        # Simple pattern detection - can be enhanced
        if pattern_name == "global_variables" and context.get("language") == "python":
            # Check for global keyword usage (simplified)
            return "global " in context.get("code_context", "")

        if pattern_name == "var_usage" and context.get("language") == "javascript":
            return "var " in context.get("code_context", "")

        return False

    def _analyze_performance_data(self, project_path: str) -> Dict[str, Any]:
        """Analyze performance-related data for the project."""
        try:
            project_dir = Path(project_path)

            large_files = 0
            deep_nesting = 0
            total_files = 0

            for file_path in project_dir.rglob("*"):
                if file_path.is_file():
                    total_files += 1

                    # Check file size
                    if file_path.stat().st_size > 100 * 1024:
                        large_files += 1

                    # Check directory depth
                    depth = len(file_path.relative_to(project_dir).parts)
                    deep_nesting = max(deep_nesting, depth)

            return {
                "large_files_count": large_files,
                "deep_nesting": deep_nesting,
                "total_files": total_files
            }

        except Exception as e:
            logger.error(f"Performance data analysis failed: {e}")
            return {}

    def _load_learning_data(self) -> None:
        """Load existing learning data."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Load successful patterns
                cursor = conn.execute("""
                    SELECT pattern_type, pattern_data, success_rate, usage_count
                    FROM learning_patterns
                    ORDER BY success_rate DESC
                """)

                for row in cursor:
                    pattern_type, pattern_data, success_rate, usage_count = row
                    self.learning_data["project_patterns"][pattern_type] = {
                        "data": json.loads(pattern_data),
                        "success_rate": success_rate,
                        "usage_count": usage_count
                    }

        except Exception as e:
            logger.debug(f"Failed to load learning data: {e}")

    def _update_successful_patterns(self, suggestion_id: str) -> None:
        """Update patterns based on successful suggestions."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get suggestion details
                cursor = conn.execute("""
                    SELECT category, context_data FROM suggestions_history WHERE id = ?
                """, (suggestion_id,))

                row = cursor.fetchone()
                if row:
                    category, context_data = row

                    # Update pattern success rate
                    conn.execute("""
                        INSERT OR REPLACE INTO learning_patterns
                        (pattern_type, pattern_data, success_rate, usage_count, last_used)
                        VALUES (?, ?,
                                COALESCE((SELECT success_rate FROM learning_patterns WHERE pattern_type = ?) + 0.1, 0.1),
                                COALESCE((SELECT usage_count FROM learning_patterns WHERE pattern_type = ?) + 1, 1),
                                julianday('now'))
                    """, (category, context_data, category, category))

        except Exception as e:
            logger.error(f"Failed to update successful patterns: {e}")

    def _update_rejected_patterns(self, suggestion_id: str) -> None:
        """Update patterns based on rejected suggestions."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Get suggestion details
                cursor = conn.execute("""
                    SELECT category FROM suggestions_history WHERE id = ?
                """, (suggestion_id,))

                row = cursor.fetchone()
                if row:
                    category = row[0]

                    # Decrease pattern success rate
                    conn.execute("""
                        UPDATE learning_patterns
                        SET success_rate = MAX(0.0, success_rate - 0.05)
                        WHERE pattern_type = ?
                    """, (category,))

        except Exception as e:
            logger.error(f"Failed to update rejected patterns: {e}")

    def _adapt_suggestion_algorithms(self) -> None:
        """Adapt suggestion algorithms based on learning."""
        try:
            # Calculate acceptance rate
            total_suggestions = self.performance_metrics["suggestions_generated"]
            accepted_suggestions = self.performance_metrics["suggestions_accepted"]

            if total_suggestions > 0:
                acceptance_rate = accepted_suggestions / total_suggestions
                self.performance_metrics["analysis_accuracy"] = acceptance_rate

                # Adjust confidence thresholds based on acceptance rate
                if acceptance_rate < 0.3:
                    # Lower acceptance rate - be more conservative
                    for patterns in self.known_patterns.values():
                        for pattern in patterns.get("good_patterns", []):
                            pattern["confidence"] = max(0.5, pattern["confidence"] - 0.1)
                elif acceptance_rate > 0.7:
                    # Higher acceptance rate - be more aggressive
                    for patterns in self.known_patterns.values():
                        for pattern in patterns.get("good_patterns", []):
                            pattern["confidence"] = min(1.0, pattern["confidence"] + 0.05)

        except Exception as e:
            logger.error(f"Algorithm adaptation failed: {e}")

    def get_intelligence_metrics(self) -> Dict[str, Any]:
        """Get intelligence engine performance metrics."""
        return {
            **self.performance_metrics,
            "current_context": self.current_context,
            "known_patterns_count": sum(
                len(patterns.get("good_patterns", [])) + len(patterns.get("anti_patterns", []))
                for patterns in self.known_patterns.values()
            ),
            "learning_data_size": len(self.learning_data.get("project_patterns", {}))
        }


# Global intelligence engine instance
_intelligence_engine: Optional[IntelligenceEngine] = None


def get_intelligence_engine(workspace_path: Optional[str] = None) -> IntelligenceEngine:
    """Get the global intelligence engine instance."""
    global _intelligence_engine
    if _intelligence_engine is None:
        _intelligence_engine = IntelligenceEngine(workspace_path)
    return _intelligence_engine
