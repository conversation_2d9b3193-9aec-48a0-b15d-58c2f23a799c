"""
Superior AI Tool for Enhanced Coding Capabilities.

This tool integrates the superior AI agent capabilities to provide
advanced code understanding, intelligent completion, refactoring,
and debugging assistance that surpasses competing coding assistants.
"""

import logging
from typing import Dict, Any, List, Optional, Generator
from pathlib import Path

from tools.base import ToolSpec, Parameter
from tools.registry import register_tool
from message import Message

try:
    from superior_ai_agent import (
        SuperiorAIAgent, CodeQuality, RefactoringType,
        CodeSuggestion, CodeCompletion, DebugInsight
    )
    SUPERIOR_AI_AVAILABLE = True
except ImportError:
    SUPERIOR_AI_AVAILABLE = False

logger = logging.getLogger(__name__)


class SuperiorAITool(ToolSpec):
    """
    Superior AI tool for advanced coding assistance.
    
    Features:
    - Advanced code quality analysis
    - Intelligent code completion
    - Sophisticated refactoring suggestions
    - Advanced debugging insights
    - Architecture analysis and recommendations
    """
    
    def __init__(self):
        parameters = [
            Parameter(
                name="action",
                type="string",
                description="AI assistance action to perform",
                required=True,
                enum=[
                    "analyze_quality", "suggest_completion", "suggest_refactoring",
                    "debug_assistance", "architecture_analysis", "performance_metrics"
                ]
            ),
            Parameter(
                name="code",
                type="string",
                description="Source code to analyze",
                required=False
            ),
            Parameter(
                name="language",
                type="string",
                description="Programming language",
                required=False,
                enum=["python", "javascript", "typescript", "java", "c", "cpp", "csharp", "go", "rust"]
            ),
            Parameter(
                name="file_path",
                type="string",
                description="File path for context",
                required=False
            ),
            Parameter(
                name="cursor_position",
                type="integer",
                description="Cursor position for completion (character index)",
                required=False
            ),
            Parameter(
                name="error_message",
                type="string",
                description="Error message for debugging assistance",
                required=False
            ),
            Parameter(
                name="project_path",
                type="string",
                description="Project root path for architecture analysis",
                required=False
            ),
            Parameter(
                name="context_lines",
                type="integer",
                description="Number of context lines to include",
                required=False,
                default=10
            )
        ]
        
        super().__init__(
            name="superior_ai",
            description="Superior AI assistant for advanced coding capabilities including intelligent completion, refactoring, debugging, and architecture analysis",
            parameters=parameters,
            block_types=["ai_assistant", "code_analysis", "intelligent_completion"]
        )
        
        # Initialize superior AI agent
        self.ai_agent = None
        if SUPERIOR_AI_AVAILABLE:
            try:
                self.ai_agent = SuperiorAIAgent()
                logger.info("Superior AI agent initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize superior AI agent: {e}")
    
    def is_available(self) -> bool:
        """Check if the superior AI tool is available."""
        return SUPERIOR_AI_AVAILABLE and self.ai_agent is not None
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute superior AI assistance."""
        if not self.is_available():
            yield self.create_response("❌ Superior AI capabilities not available")
            return
        
        try:
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            action = params["action"]
            
            yield self.create_response(f"🤖 **Superior AI Assistant** - {action.replace('_', ' ').title()}")
            
            # Execute action
            if action == "analyze_quality":
                yield from self._analyze_code_quality(params)
            elif action == "suggest_completion":
                yield from self._suggest_completion(params)
            elif action == "suggest_refactoring":
                yield from self._suggest_refactoring(params)
            elif action == "debug_assistance":
                yield from self._debug_assistance(params)
            elif action == "architecture_analysis":
                yield from self._architecture_analysis(params)
            elif action == "performance_metrics":
                yield from self._performance_metrics(params)
            else:
                yield self.create_response(f"Unknown action: {action}")
        
        except Exception as e:
            logger.error(f"Superior AI tool error: {e}")
            yield self.create_response(f"Error in superior AI assistance: {str(e)}")
    
    def _analyze_code_quality(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze code quality with advanced metrics."""
        code = params.get("code", "")
        language = params.get("language", "python")
        file_path = params.get("file_path", "")
        
        if not code:
            yield self.create_response("❌ No code provided for analysis")
            return
        
        yield self.create_response("🔍 Analyzing code quality...")
        
        # Perform quality analysis
        analysis = self.ai_agent.analyze_code_quality(code, language, file_path)
        
        # Format results
        quality_emoji = {
            "excellent": "🟢",
            "good": "🔵", 
            "fair": "🟡",
            "poor": "🟠",
            "critical": "🔴"
        }
        
        quality_level = analysis["overall_quality"].value
        emoji = quality_emoji.get(quality_level, "⚪")
        
        report = [
            f"## {emoji} Code Quality Analysis",
            f"**Overall Quality:** {quality_level.title()} ({analysis['quality_score']:.1%})",
            "",
            "### 📊 Metrics"
        ]
        
        for metric, value in analysis["metrics"].items():
            report.append(f"- **{metric.replace('_', ' ').title()}:** {value}")
        
        if analysis["issues"]:
            report.extend(["", "### ⚠️ Issues"])
            for issue in analysis["issues"]:
                report.append(f"- {issue}")
        
        if analysis["strengths"]:
            report.extend(["", "### ✅ Strengths"])
            for strength in analysis["strengths"]:
                report.append(f"- {strength}")
        
        if analysis["suggestions"]:
            report.extend(["", "### 💡 Suggestions"])
            for suggestion in analysis["suggestions"]:
                report.append(f"- {suggestion}")
        
        yield self.create_response("\n".join(report))
    
    def _suggest_completion(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Provide intelligent code completion suggestions."""
        code = params.get("code", "")
        language = params.get("language", "python")
        cursor_position = params.get("cursor_position", len(code))
        file_path = params.get("file_path", "")
        
        if not code:
            yield self.create_response("❌ No code context provided for completion")
            return
        
        yield self.create_response("💡 Generating intelligent completions...")
        
        # Generate completions
        completions = self.ai_agent.generate_intelligent_completion(
            code, cursor_position, language, file_path
        )
        
        if not completions:
            yield self.create_response("No completions available for the current context")
            return
        
        # Format completions
        report = ["## 💡 Intelligent Code Completions", ""]
        
        for i, completion in enumerate(completions[:5], 1):
            confidence_bar = "🟢" * int(completion.confidence * 5)
            report.extend([
                f"### {i}. {completion.completion_type.title()} Completion",
                f"**Confidence:** {confidence_bar} ({completion.confidence:.1%})",
                f"**Code:** `{completion.completion_text}`",
                f"**Context Aware:** {'✅' if completion.context_aware else '❌'}"
            ])
            
            if completion.documentation:
                report.append(f"**Description:** {completion.documentation}")
            
            report.append("")
        
        yield self.create_response("\n".join(report))
    
    def _suggest_refactoring(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Provide intelligent refactoring suggestions."""
        code = params.get("code", "")
        language = params.get("language", "python")
        file_path = params.get("file_path", "")
        
        if not code:
            yield self.create_response("❌ No code provided for refactoring analysis")
            return
        
        yield self.create_response("🔧 Analyzing code for refactoring opportunities...")
        
        # Generate refactoring suggestions
        suggestions = self.ai_agent.suggest_refactoring(code, language, file_path)
        
        if not suggestions:
            yield self.create_response("✅ No refactoring suggestions - code looks good!")
            return
        
        # Format suggestions
        report = ["## 🔧 Refactoring Suggestions", ""]
        
        impact_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}
        
        for i, suggestion in enumerate(suggestions[:5], 1):
            confidence_bar = "🟢" * int(suggestion.confidence * 5)
            impact_emoji_val = impact_emoji.get(suggestion.impact, "⚪")
            
            report.extend([
                f"### {i}. {suggestion.title}",
                f"**Type:** {suggestion.suggestion_type.value.replace('_', ' ').title()}",
                f"**Impact:** {impact_emoji_val} {suggestion.impact.title()}",
                f"**Confidence:** {confidence_bar} ({suggestion.confidence:.1%})",
                f"**Location:** Lines {suggestion.line_start}-{suggestion.line_end}",
                "",
                f"**Description:** {suggestion.description}",
                f"**Reasoning:** {suggestion.reasoning}",
                ""
            ])
            
            if suggestion.tags:
                report.append(f"**Tags:** {', '.join(suggestion.tags)}")
                report.append("")
        
        yield self.create_response("\n".join(report))
    
    def _debug_assistance(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Provide intelligent debugging assistance."""
        code = params.get("code", "")
        error_message = params.get("error_message", "")
        language = params.get("language", "python")
        file_path = params.get("file_path", "")
        
        if not error_message:
            yield self.create_response("❌ No error message provided for debugging")
            return
        
        yield self.create_response("🐛 Analyzing error and providing debugging insights...")
        
        # Generate debug insights
        insights = self.ai_agent.provide_debug_insights(code, error_message, language, file_path)
        
        if not insights:
            yield self.create_response("No specific debugging insights available")
            return
        
        # Format insights
        report = ["## 🐛 Debugging Insights", ""]
        
        severity_emoji = {"error": "🔴", "warning": "🟡", "info": "🔵"}
        
        for i, insight in enumerate(insights[:5], 1):
            severity_emoji_val = severity_emoji.get(insight.severity, "⚪")
            confidence_bar = "🟢" * int(insight.confidence * 5)
            
            report.extend([
                f"### {i}. {insight.issue_type.replace('_', ' ').title()}",
                f"**Severity:** {severity_emoji_val} {insight.severity.title()}",
                f"**Confidence:** {confidence_bar} ({insight.confidence:.1%})",
                "",
                f"**Issue:** {insight.description}",
                f"**Suggested Fix:** {insight.suggested_fix}",
                ""
            ])
            
            if insight.related_files:
                report.append(f"**Related Files:** {', '.join(insight.related_files)}")
                report.append("")
        
        yield self.create_response("\n".join(report))
    
    def _architecture_analysis(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Analyze project architecture."""
        project_path = params.get("project_path", ".")
        
        yield self.create_response("🏗️ Analyzing project architecture...")
        
        # Perform architecture analysis
        analysis = self.ai_agent.understand_code_architecture(project_path)
        
        # Format results
        report = [
            "## 🏗️ Architecture Analysis",
            f"**Architecture Type:** {analysis['architecture_type'].title()}",
            f"**Structure Quality:** {analysis['structure_quality'].title()}",
            f"**Complexity Score:** {analysis['complexity_score']:.1%}",
            f"**Maintainability Score:** {analysis['maintainability_score']:.1%}",
            ""
        ]
        
        if analysis["patterns_detected"]:
            report.extend(["### 🎯 Detected Patterns"])
            for pattern in analysis["patterns_detected"]:
                report.append(f"- {pattern}")
            report.append("")
        
        if analysis["recommendations"]:
            report.extend(["### 💡 Recommendations"])
            for recommendation in analysis["recommendations"]:
                report.append(f"- {recommendation}")
        
        yield self.create_response("\n".join(report))
    
    def _performance_metrics(self, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Show AI agent performance metrics."""
        yield self.create_response("📊 Retrieving AI agent performance metrics...")
        
        # Get performance metrics
        metrics = self.ai_agent.get_performance_metrics()
        
        # Format metrics
        report = [
            "## 📊 Superior AI Agent Performance",
            f"**Suggestions Generated:** {metrics['suggestions_generated']:,}",
            f"**Suggestions Accepted:** {metrics['suggestions_accepted']:,}",
            f"**Completions Generated:** {metrics['completions_generated']:,}",
            f"**Debug Insights Provided:** {metrics['debug_insights_provided']:,}",
            f"**Acceptance Rate:** {metrics['acceptance_rate']:.1%}",
            f"**Efficiency Score:** {metrics['efficiency_score']:.1%}",
            f"**Accuracy Score:** {metrics['accuracy_score']:.1%}"
        ]
        
        yield self.create_response("\n".join(report))


# Register the tool
if SUPERIOR_AI_AVAILABLE:
    register_tool(SuperiorAITool())
