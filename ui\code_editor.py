"""
Modern Code Editor for Enhanced AI Coding Agent.

This module provides a code editor with syntax highlighting,
line numbers, and modern editing features.
"""

import logging
from typing import Optional, Dict, Any, List, Callable, Tuple
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.syntax import Syntax
    from rich.table import Table
    from rich.columns import Columns
    from rich.align import Align
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

from ui.syntax_highlighter import SyntaxHighlighter

logger = logging.getLogger(__name__)


class EditorMode(Enum):
    """Editor modes."""
    VIEW = "view"
    EDIT = "edit"
    DIFF = "diff"


@dataclass
class EditorState:
    """Editor state information."""
    file_path: Optional[Path] = None
    content: str = ""
    language: str = "text"
    cursor_line: int = 1
    cursor_column: int = 1
    selection_start: Optional[Tuple[int, int]] = None
    selection_end: Optional[Tuple[int, int]] = None
    is_modified: bool = False
    mode: EditorMode = EditorMode.VIEW


class CodeEditor:
    """
    Modern code editor with syntax highlighting and editing features.
    
    Features:
    - Syntax highlighting for multiple languages
    - Line numbers and column indicators
    - File operations (open, save, new)
    - Search and replace
    - Modern styling with themes
    - Diff view for changes
    - Auto-indentation and bracket matching
    """
    
    def __init__(
        self,
        theme: str = "dark",
        font_size: int = 14,
        font_family: str = "JetBrains Mono",
        tab_size: int = 4,
        show_line_numbers: bool = True
    ):
        """
        Initialize the code editor.
        
        Args:
            theme: UI theme
            font_size: Font size
            font_family: Font family
            tab_size: Tab size in spaces
            show_line_numbers: Whether to show line numbers
        """
        self.theme = theme
        self.font_size = font_size
        self.font_family = font_family
        self.tab_size = tab_size
        self.show_line_numbers = show_line_numbers
        
        self.console = Console() if RICH_AVAILABLE else None
        self.syntax_highlighter = SyntaxHighlighter(theme=theme)
        
        self.state = EditorState()
        self.undo_stack: List[str] = []
        self.redo_stack: List[str] = []
        
        # Theme colors
        self._setup_theme_colors()
        
        # Language detection
        self.language_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.tsx': 'typescript',
            '.jsx': 'javascript',
            '.html': 'html',
            '.htm': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.json': 'json',
            '.md': 'markdown',
            '.yaml': 'yaml',
            '.yml': 'yaml',
            '.toml': 'toml',
            '.xml': 'xml',
            '.sql': 'sql',
            '.sh': 'bash',
            '.bash': 'bash',
            '.zsh': 'zsh',
            '.fish': 'fish',
            '.ps1': 'powershell',
            '.rs': 'rust',
            '.go': 'go',
            '.java': 'java',
            '.c': 'c',
            '.cpp': 'cpp',
            '.h': 'c',
            '.hpp': 'cpp'
        }
    
    def _setup_theme_colors(self) -> None:
        """Setup theme-specific colors."""
        if self.theme == "dark":
            self.colors = {
                "background": "#0d1117",
                "foreground": "#f0f6fc",
                "line_number": "#6b7280",
                "current_line": "#161b22",
                "selection": "#264f78",
                "cursor": "#58a6ff",
                "border": "#30363d",
                "modified": "#fbbf24",
                "error": "#f87171",
                "warning": "#fbbf24"
            }
        elif self.theme == "light":
            self.colors = {
                "background": "#ffffff",
                "foreground": "#24292f",
                "line_number": "#656d76",
                "current_line": "#f6f8fa",
                "selection": "#b3d4fc",
                "cursor": "#0969da",
                "border": "#d0d7de",
                "modified": "#bf8700",
                "error": "#d1242f",
                "warning": "#bf8700"
            }
        else:  # claude theme
            self.colors = {
                "background": "#fefefe",
                "foreground": "#1e293b",
                "line_number": "#64748b",
                "current_line": "#f8fafc",
                "selection": "#dbeafe",
                "cursor": "#2563eb",
                "border": "#e2e8f0",
                "modified": "#d97706",
                "error": "#dc2626",
                "warning": "#d97706"
            }
    
    def _detect_language(self, file_path: Optional[Path]) -> str:
        """Detect programming language from file extension."""
        if not file_path:
            return "text"
        
        suffix = file_path.suffix.lower()
        return self.language_map.get(suffix, "text")
    
    def open_file(self, file_path: str) -> bool:
        """
        Open a file in the editor.
        
        Args:
            file_path: Path to the file to open
        
        Returns:
            True if successful, False otherwise
        """
        try:
            path = Path(file_path)
            if not path.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
            
            content = path.read_text(encoding='utf-8')
            language = self._detect_language(path)
            
            # Save current state to undo stack
            if self.state.content:
                self.undo_stack.append(self.state.content)
            
            self.state = EditorState(
                file_path=path,
                content=content,
                language=language,
                cursor_line=1,
                cursor_column=1,
                is_modified=False,
                mode=EditorMode.VIEW
            )
            
            logger.info(f"Opened file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to open file {file_path}: {e}")
            return False
    
    def save_file(self, file_path: Optional[str] = None) -> bool:
        """
        Save the current content to a file.
        
        Args:
            file_path: Path to save to (uses current file if None)
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if file_path:
                path = Path(file_path)
            elif self.state.file_path:
                path = self.state.file_path
            else:
                logger.error("No file path specified for save")
                return False
            
            # Create parent directories if needed
            path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content
            path.write_text(self.state.content, encoding='utf-8')
            
            # Update state
            self.state.file_path = path
            self.state.is_modified = False
            
            logger.info(f"Saved file: {path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save file: {e}")
            return False
    
    def new_file(self, language: str = "text") -> None:
        """Create a new file."""
        # Save current state to undo stack
        if self.state.content:
            self.undo_stack.append(self.state.content)
        
        self.state = EditorState(
            content="",
            language=language,
            cursor_line=1,
            cursor_column=1,
            is_modified=False,
            mode=EditorMode.EDIT
        )
    
    def set_content(self, content: str, language: Optional[str] = None) -> None:
        """Set editor content."""
        # Save current state to undo stack
        if self.state.content:
            self.undo_stack.append(self.state.content)
        
        self.state.content = content
        if language:
            self.state.language = language
        self.state.is_modified = True
        self.state.cursor_line = 1
        self.state.cursor_column = 1
    
    def get_content(self) -> str:
        """Get current editor content."""
        return self.state.content
    
    def insert_text(self, text: str, line: Optional[int] = None, column: Optional[int] = None) -> None:
        """Insert text at specified position or cursor."""
        if line is None:
            line = self.state.cursor_line
        if column is None:
            column = self.state.cursor_column
        
        lines = self.state.content.split('\n')
        
        # Ensure we have enough lines
        while len(lines) < line:
            lines.append("")
        
        # Insert text
        if line <= len(lines):
            current_line = lines[line - 1]
            new_line = current_line[:column - 1] + text + current_line[column - 1:]
            lines[line - 1] = new_line
        
        self.state.content = '\n'.join(lines)
        self.state.is_modified = True
    
    def delete_text(self, start_line: int, start_column: int, end_line: int, end_column: int) -> None:
        """Delete text in specified range."""
        lines = self.state.content.split('\n')
        
        if start_line == end_line:
            # Single line deletion
            if start_line <= len(lines):
                current_line = lines[start_line - 1]
                new_line = current_line[:start_column - 1] + current_line[end_column - 1:]
                lines[start_line - 1] = new_line
        else:
            # Multi-line deletion
            if start_line <= len(lines) and end_line <= len(lines):
                # Keep part of first line
                first_part = lines[start_line - 1][:start_column - 1]
                # Keep part of last line
                last_part = lines[end_line - 1][end_column - 1:]
                # Combine and remove middle lines
                lines = lines[:start_line - 1] + [first_part + last_part] + lines[end_line:]
        
        self.state.content = '\n'.join(lines)
        self.state.is_modified = True
    
    def find_text(self, pattern: str, case_sensitive: bool = False) -> List[Tuple[int, int]]:
        """Find all occurrences of text pattern."""
        import re
        
        flags = 0 if case_sensitive else re.IGNORECASE
        matches = []
        
        lines = self.state.content.split('\n')
        for line_num, line in enumerate(lines, 1):
            for match in re.finditer(pattern, line, flags):
                matches.append((line_num, match.start() + 1))
        
        return matches
    
    def replace_text(self, pattern: str, replacement: str, case_sensitive: bool = False) -> int:
        """Replace all occurrences of text pattern."""
        import re
        
        flags = 0 if case_sensitive else re.IGNORECASE
        original_content = self.state.content
        
        self.state.content = re.sub(pattern, replacement, self.state.content, flags=flags)
        
        # Count replacements
        count = len(re.findall(pattern, original_content, flags))
        
        if count > 0:
            self.state.is_modified = True
        
        return count
    
    def undo(self) -> bool:
        """Undo last change."""
        if self.undo_stack:
            self.redo_stack.append(self.state.content)
            self.state.content = self.undo_stack.pop()
            self.state.is_modified = True
            return True
        return False
    
    def redo(self) -> bool:
        """Redo last undone change."""
        if self.redo_stack:
            self.undo_stack.append(self.state.content)
            self.state.content = self.redo_stack.pop()
            self.state.is_modified = True
            return True
        return False
    
    def render_editor(self, width: int = 80, height: int = 24) -> Panel:
        """
        Render the code editor.
        
        Args:
            width: Editor width
            height: Editor height
        
        Returns:
            Rich Panel with editor content
        """
        if not RICH_AVAILABLE:
            return self._render_text_editor()
        
        # Get syntax highlighted content
        if self.syntax_highlighter and self.state.language != "text":
            content = self.syntax_highlighter.highlight_code(
                self.state.content,
                self.state.language
            )
        else:
            content = Text(self.state.content)
        
        # Add line numbers if enabled
        if self.show_line_numbers:
            content = self._add_line_numbers(content)
        
        # Create title
        title = "📝 Code Editor"
        if self.state.file_path:
            title += f" - {self.state.file_path.name}"
        if self.state.is_modified:
            title += " •"
        
        # Create panel
        panel = Panel(
            content,
            title=title,
            title_align="left",
            border_style=self.colors["border"],
            padding=(1, 1)
        )
        
        return panel
    
    def _add_line_numbers(self, content) -> Table:
        """Add line numbers to content."""
        if not RICH_AVAILABLE:
            return content
        
        table = Table.grid(padding=(0, 1))
        table.add_column(style=self.colors["line_number"], width=4, justify="right")
        table.add_column()
        
        lines = str(content).split('\n')
        for i, line in enumerate(lines, 1):
            # Highlight current line
            line_style = self.colors["current_line"] if i == self.state.cursor_line else None
            table.add_row(str(i), line, style=line_style)
        
        return table
    
    def _render_text_editor(self) -> str:
        """Render a simple text editor for fallback."""
        lines = []
        
        # Title
        title = "Code Editor"
        if self.state.file_path:
            title += f" - {self.state.file_path.name}"
        if self.state.is_modified:
            title += " (modified)"
        
        lines.append(title)
        lines.append("=" * len(title))
        
        # Content with line numbers
        content_lines = self.state.content.split('\n')
        for i, line in enumerate(content_lines, 1):
            prefix = f"{i:4d} | "
            if i == self.state.cursor_line:
                prefix = f"{i:4d} > "  # Mark current line
            lines.append(prefix + line)
        
        return '\n'.join(lines)
    
    def get_status_info(self) -> Dict[str, Any]:
        """Get editor status information."""
        lines = self.state.content.split('\n')
        
        return {
            "file_path": str(self.state.file_path) if self.state.file_path else None,
            "language": self.state.language,
            "line_count": len(lines),
            "cursor_line": self.state.cursor_line,
            "cursor_column": self.state.cursor_column,
            "is_modified": self.state.is_modified,
            "mode": self.state.mode.value,
            "character_count": len(self.state.content),
            "word_count": len(self.state.content.split()),
            "selection_active": self.state.selection_start is not None
        }
