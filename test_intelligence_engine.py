#!/usr/bin/env python3
"""
Test script for the Intelligence Engine.

This script tests the advanced intelligence and performance improvements
including context-aware suggestions, project architecture analysis,
and adaptive learning capabilities.
"""

import sys
import tempfile
import json
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from intelligence_engine import (
    IntelligenceEngine, IntelligentSuggestion, ProjectInsight,
    SuggestionCategory, IntelligenceLevel, get_intelligence_engine
)


def create_test_project(base_path: Path) -> None:
    """Create a test project structure."""
    print("Creating test project structure...")
    
    # Create directories
    (base_path / "src").mkdir()
    (base_path / "tests").mkdir()
    (base_path / "docs").mkdir()
    (base_path / "config").mkdir()
    
    # Create Python files
    (base_path / "src" / "__init__.py").write_text("")
    (base_path / "src" / "main.py").write_text('''
import os
import sys
from typing import List, Dict

class UserManager:
    def __init__(self):
        self.users = []
    
    def add_user(self, user_data):
        self.users.append(user_data)
    
    def get_users(self):
        return self.users

def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
        else:
            result.append(0)
    return result

if __name__ == "__main__":
    manager = UserManager()
    manager.add_user({"name": "John", "age": 30})
    print(manager.get_users())
''')
    
    # Create a large file to test performance suggestions
    large_content = "# Large file for testing\n" + "def function_{}():\n    pass\n\n" * 200
    (base_path / "src" / "large_file.py").write_text(large_content)
    
    # Create test files
    (base_path / "tests" / "test_main.py").write_text('''
import unittest
from src.main import UserManager, process_data

class TestUserManager(unittest.TestCase):
    def test_add_user(self):
        manager = UserManager()
        manager.add_user({"name": "Test"})
        self.assertEqual(len(manager.get_users()), 1)

if __name__ == "__main__":
    unittest.main()
''')
    
    # Create documentation
    (base_path / "docs" / "README.md").write_text('''
# Test Project

This is a test project for the intelligence engine.

## Features
- User management
- Data processing
- Testing framework
''')
    
    # Create configuration files
    (base_path / "config" / "settings.json").write_text(json.dumps({
        "database_url": "sqlite:///test.db",
        "debug": True,
        "secret_key": "test-secret-key"
    }, indent=2))
    
    # Create package.json for JavaScript detection
    (base_path / "package.json").write_text(json.dumps({
        "name": "test-project",
        "version": "1.0.0",
        "dependencies": {
            "react": "^18.0.0",
            "lodash": "^4.17.21"
        }
    }, indent=2))
    
    print("✅ Test project structure created")


def test_intelligence_initialization():
    """Test intelligence engine initialization."""
    print("\n🧪 Testing intelligence engine initialization...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        create_test_project(temp_path)
        
        # Initialize intelligence engine
        engine = IntelligenceEngine(str(temp_path))
        
        print(f"✅ Engine initialized with workspace: {temp_path}")
        print(f"✅ Current context: {engine.current_context}")
        print(f"✅ Known patterns: {len(engine.known_patterns)} languages")
        
        return True


def test_project_intelligence_analysis():
    """Test project intelligence analysis."""
    print("\n🧪 Testing project intelligence analysis...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        create_test_project(temp_path)
        
        engine = IntelligenceEngine(str(temp_path))
        
        # Analyze project intelligence
        insights = engine.analyze_project_intelligence(str(temp_path))
        
        print(f"✅ Generated {len(insights)} project insights")
        
        for insight in insights:
            print(f"   - {insight.insight_type}: {insight.title}")
            print(f"     Confidence: {insight.confidence:.1%}")
            print(f"     Recommendations: {len(insight.recommendations)}")
        
        return True


def test_context_aware_suggestions():
    """Test context-aware suggestion generation."""
    print("\n🧪 Testing context-aware suggestions...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        create_test_project(temp_path)
        
        engine = IntelligenceEngine(str(temp_path))
        
        # Test Python code context
        python_code = '''
def long_function_with_many_lines():
    # This function is intentionally long to trigger suggestions
    data = []
    for i in range(100):
        if i % 2 == 0:
            data.append(i)
        else:
            data.append(i * 2)
    
    result = []
    for item in data:
        if item > 50:
            result.append(item)
    
    return result
'''
        
        suggestions = engine.generate_context_aware_suggestions(
            python_code, "test.py", {"language": "python"}
        )
        
        print(f"✅ Generated {len(suggestions)} context-aware suggestions")
        
        for suggestion in suggestions[:3]:  # Show top 3
            print(f"   - {suggestion.category.value}: {suggestion.title}")
            print(f"     Confidence: {suggestion.confidence:.1%}")
            print(f"     Impact: {suggestion.impact_score:.1%}")
        
        return True


def test_performance_recommendations():
    """Test performance recommendation generation."""
    print("\n🧪 Testing performance recommendations...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        create_test_project(temp_path)
        
        engine = IntelligenceEngine(str(temp_path))
        
        # Get performance recommendations
        recommendations = engine.get_performance_recommendations(str(temp_path))
        
        print(f"✅ Generated {len(recommendations)} performance recommendations")
        
        for rec in recommendations:
            print(f"   - {rec.title}")
            print(f"     Effort: {rec.implementation_effort}")
            print(f"     Impact: {rec.impact_score:.1%}")
        
        return True


def test_learning_and_feedback():
    """Test learning and feedback mechanisms."""
    print("\n🧪 Testing learning and feedback...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        create_test_project(temp_path)
        
        engine = IntelligenceEngine(str(temp_path))
        
        # Generate suggestions
        suggestions = engine.generate_context_aware_suggestions(
            "def test(): pass", "test.py"
        )
        
        if suggestions:
            # Simulate feedback
            suggestion_id = "1"  # Mock ID
            engine.learn_from_feedback(suggestion_id, accepted=True, feedback="Very helpful")
            
            print("✅ Positive feedback processed")
            
            # Simulate negative feedback
            engine.learn_from_feedback(suggestion_id, accepted=False, feedback="Not relevant")
            
            print("✅ Negative feedback processed")
        
        # Check performance metrics
        metrics = engine.get_intelligence_metrics()
        print(f"✅ Performance metrics: {metrics['suggestions_generated']} suggestions generated")
        
        return True


def test_pattern_recognition():
    """Test pattern recognition capabilities."""
    print("\n🧪 Testing pattern recognition...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        create_test_project(temp_path)
        
        engine = IntelligenceEngine(str(temp_path))
        
        # Test anti-pattern detection
        bad_code = '''
var globalVariable = "bad practice";

function badFunction() {
    var x = 1;
    var y = 2;
    return x + y;
}
'''
        
        suggestions = engine.generate_context_aware_suggestions(
            bad_code, "bad.js", {"language": "javascript"}
        )
        
        # Check for anti-pattern suggestions
        anti_pattern_suggestions = [
            s for s in suggestions 
            if "anti-pattern" in s.tags or "var" in s.title.lower()
        ]
        
        print(f"✅ Detected {len(anti_pattern_suggestions)} anti-pattern suggestions")
        
        return True


def test_architecture_analysis():
    """Test architecture analysis capabilities."""
    print("\n🧪 Testing architecture analysis...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create MVC-like structure
        (temp_path / "models").mkdir()
        (temp_path / "views").mkdir()
        (temp_path / "controllers").mkdir()
        
        (temp_path / "models" / "user.py").write_text("class User: pass")
        (temp_path / "views" / "user_view.py").write_text("class UserView: pass")
        (temp_path / "controllers" / "user_controller.py").write_text("class UserController: pass")
        
        engine = IntelligenceEngine(str(temp_path))
        
        # Analyze architecture
        insights = engine.analyze_project_intelligence(str(temp_path))
        
        # Check for architecture insights
        arch_insights = [i for i in insights if i.insight_type == "architecture"]
        
        print(f"✅ Generated {len(arch_insights)} architecture insights")
        
        if arch_insights:
            insight = arch_insights[0]
            print(f"   Architecture: {insight.description}")
            print(f"   Recommendations: {len(insight.recommendations)}")
        
        return True


def test_global_intelligence_engine():
    """Test global intelligence engine instance."""
    print("\n🧪 Testing global intelligence engine...")
    
    # Get global instance
    engine1 = get_intelligence_engine(".")
    engine2 = get_intelligence_engine(".")
    
    # Should be the same instance
    assert engine1 is engine2, "Global instance should be singleton"
    
    print("✅ Global intelligence engine works correctly")
    
    return True


def main():
    """Run all intelligence engine tests."""
    print("🚀 Intelligence Engine Test Suite")
    print("=" * 50)
    
    try:
        # Run tests
        test_intelligence_initialization()
        test_project_intelligence_analysis()
        test_context_aware_suggestions()
        test_performance_recommendations()
        test_learning_and_feedback()
        test_pattern_recognition()
        test_architecture_analysis()
        test_global_intelligence_engine()
        
        print("\n" + "=" * 50)
        print("✅ All intelligence engine tests passed!")
        print("🧠 Intelligence and performance improvements are working correctly")
        print("🎯 Context-aware suggestions and adaptive learning operational!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
