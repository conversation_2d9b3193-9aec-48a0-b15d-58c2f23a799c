#!/usr/bin/env python3
"""
Test script for the Large Codebase Optimizer.

This script tests the optimization capabilities for handling massive codebases
including parallel processing, caching, and incremental analysis.
"""

import sys
import os
import tempfile
import time
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from large_codebase_optimizer import (
    LargeCodebaseOptimizer, OptimizationLevel, ProcessingStrategy,
    analyze_file_optimized, create_optimized_analyzer
)


def create_test_codebase(base_path: Path, num_files: int = 100) -> None:
    """Create a test codebase with specified number of files."""
    print(f"Creating test codebase with {num_files} files...")
    
    # Create directory structure
    for i in range(max(1, num_files // 20)):
        dir_path = base_path / f"module_{i}"
        dir_path.mkdir(exist_ok=True)
        
        # Create Python files
        for j in range(min(20, num_files - i * 20)):
            if i * 20 + j >= num_files:
                break
                
            file_path = dir_path / f"file_{j}.py"
            content = f'''"""
Module {i} - File {j}
This is a test Python file for codebase optimization testing.
"""

import os
import sys
from typing import List, Dict, Optional

class TestClass{j}:
    """Test class for file {j}."""
    
    def __init__(self, name: str):
        self.name = name
        self.data = []
    
    def add_data(self, item: str) -> None:
        """Add data to the collection."""
        self.data.append(item)
    
    def get_data(self) -> List[str]:
        """Get all data."""
        return self.data.copy()
    
    def process_data(self) -> Dict[str, int]:
        """Process data and return statistics."""
        return {{
            "total_items": len(self.data),
            "unique_items": len(set(self.data)),
            "average_length": sum(len(item) for item in self.data) / max(1, len(self.data))
        }}

def test_function_{j}(param1: str, param2: int = 0) -> Optional[str]:
    """Test function for file {j}."""
    if param2 > 0:
        return f"{{param1}}_{{param2}}"
    return None

def complex_function_{j}():
    """A more complex function with multiple branches."""
    data = []
    for i in range(10):
        if i % 2 == 0:
            data.append(f"even_{{i}}")
        elif i % 3 == 0:
            data.append(f"divisible_by_3_{{i}}")
        else:
            data.append(f"other_{{i}}")
    
    result = []
    for item in data:
        if "even" in item:
            result.append(item.upper())
        elif "divisible" in item:
            result.append(item.lower())
        else:
            result.append(item.title())
    
    return result

if __name__ == "__main__":
    test_obj = TestClass{j}("test")
    test_obj.add_data("sample")
    print(test_obj.get_data())
    print(test_function_{j}("test", {j}))
    print(complex_function_{j}())
'''
            file_path.write_text(content)
    
    print(f"✅ Created test codebase with {num_files} files")


def test_basic_optimization():
    """Test basic optimization functionality."""
    print("\n🧪 Testing basic optimization functionality...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create small test codebase
        create_test_codebase(temp_path, 50)
        
        # Initialize optimizer
        optimizer = create_optimized_analyzer(str(temp_path))
        
        # Analyze codebase size
        metrics = optimizer.analyze_codebase_size()
        print(f"✅ Codebase metrics: {metrics.total_files} files, {metrics.total_lines:,} lines")
        
        # Get optimization strategy
        opt_level, strategy = optimizer.get_optimization_strategy()
        print(f"✅ Optimization strategy: {opt_level.value} / {strategy.value}")
        
        # Test file analysis
        test_file = next(temp_path.rglob("*.py"))
        analysis = analyze_file_optimized(str(test_file))
        print(f"✅ File analysis: {len(analysis.get('functions', []))} functions, {len(analysis.get('classes', []))} classes")
        
        return True


def test_parallel_processing():
    """Test parallel processing capabilities."""
    print("\n🧪 Testing parallel processing...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create larger test codebase
        create_test_codebase(temp_path, 200)
        
        # Initialize optimizer
        optimizer = create_optimized_analyzer(str(temp_path))
        
        # Test optimized processing
        start_time = time.time()
        results = []
        
        for result in optimizer.process_codebase_optimized(
            analysis_function=analyze_file_optimized,
            incremental=False
        ):
            results.append(result)
            if len(results) % 50 == 0:
                print(f"  Processed {len(results)} files...")
        
        processing_time = time.time() - start_time
        
        print(f"✅ Processed {len(results)} files in {processing_time:.2f}s")
        print(f"✅ Average: {processing_time / len(results):.3f}s per file")
        
        # Check success rate
        successful = sum(1 for r in results if r.success)
        print(f"✅ Success rate: {successful}/{len(results)} ({successful/len(results):.1%})")
        
        return True


def test_caching_system():
    """Test caching and incremental analysis."""
    print("\n🧪 Testing caching system...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test codebase
        create_test_codebase(temp_path, 100)
        
        # Initialize optimizer
        optimizer = create_optimized_analyzer(str(temp_path))
        
        # First run (no cache)
        print("  First run (building cache)...")
        start_time = time.time()
        results1 = list(optimizer.process_codebase_optimized(
            analysis_function=analyze_file_optimized,
            incremental=True
        ))
        first_run_time = time.time() - start_time
        
        # Second run (with cache)
        print("  Second run (using cache)...")
        start_time = time.time()
        results2 = list(optimizer.process_codebase_optimized(
            analysis_function=analyze_file_optimized,
            incremental=True
        ))
        second_run_time = time.time() - start_time
        
        # Get performance stats
        stats = optimizer.get_performance_stats()
        
        print(f"✅ First run: {first_run_time:.2f}s ({len(results1)} files)")
        print(f"✅ Second run: {second_run_time:.2f}s ({len(results2)} files)")
        print(f"✅ Cache hit rate: {stats['cache_hit_rate']:.1%}")
        print(f"✅ Speedup: {first_run_time / max(0.001, second_run_time):.1f}x")
        
        return True


def test_memory_optimization():
    """Test memory optimization features."""
    print("\n🧪 Testing memory optimization...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test codebase
        create_test_codebase(temp_path, 150)
        
        # Initialize optimizer with low memory limit
        optimizer = create_optimized_analyzer(str(temp_path))
        optimizer.config.memory_limit_mb = 100  # Low limit for testing
        
        # Process with memory optimization
        results = []
        for result in optimizer.process_codebase_optimized(
            analysis_function=analyze_file_optimized,
            incremental=False
        ):
            results.append(result)
            
            # Trigger memory optimization periodically
            if len(results) % 50 == 0:
                optimizer.optimize_for_memory()
        
        print(f"✅ Processed {len(results)} files with memory optimization")
        
        # Check final stats
        stats = optimizer.get_performance_stats()
        print(f"✅ Final stats: {stats['files_processed']} files, {stats['errors']} errors")
        
        return True


def test_large_codebase_simulation():
    """Test with simulated large codebase."""
    print("\n🧪 Testing large codebase simulation...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create larger test codebase (simulate 1000+ files)
        create_test_codebase(temp_path, 500)
        
        # Initialize optimizer
        optimizer = create_optimized_analyzer(str(temp_path))
        
        # Analyze codebase
        metrics = optimizer.analyze_codebase_size()
        print(f"  Codebase: {metrics.total_files} files, {metrics.total_lines:,} lines")
        print(f"  Complexity score: {metrics.complexity_score:.2f}")
        print(f"  Estimated processing time: {metrics.estimated_processing_time:.1f}s")
        
        # Get optimization strategy
        opt_level, strategy = optimizer.get_optimization_strategy()
        print(f"  Strategy: {opt_level.value} codebase, {strategy.value} processing")
        
        # Process with timing
        start_time = time.time()
        results = list(optimizer.process_codebase_optimized(
            analysis_function=analyze_file_optimized,
            incremental=True
        ))
        actual_time = time.time() - start_time
        
        print(f"✅ Processed {len(results)} files in {actual_time:.1f}s")
        print(f"✅ Estimation accuracy: {abs(metrics.estimated_processing_time - actual_time) / metrics.estimated_processing_time:.1%} error")
        
        return True


def main():
    """Run all large codebase optimizer tests."""
    print("🚀 Large Codebase Optimizer Test Suite")
    print("=" * 50)
    
    try:
        # Run tests
        test_basic_optimization()
        test_parallel_processing()
        test_caching_system()
        test_memory_optimization()
        test_large_codebase_simulation()
        
        print("\n" + "=" * 50)
        print("✅ All large codebase optimizer tests passed!")
        print("🚀 Large codebase optimization is working correctly")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
