"""
Modern File Explorer for Enhanced AI Coding Agent.

This module provides a beautiful file explorer with tree view,
file operations, and modern styling.
"""

import os
import logging
from typing import Optional, Dict, Any, List, Callable, Set
from pathlib import Path
from dataclasses import dataclass, field
from enum import Enum

try:
    from rich.console import Console
    from rich.tree import Tree
    from rich.panel import Panel
    from rich.text import Text
    from rich.table import Table
    from rich.columns import Columns
    from rich.align import Align
    from rich.filesize import decimal
    from rich.markup import escape
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

logger = logging.getLogger(__name__)


class FileType(Enum):
    """File type categories."""
    DIRECTORY = "directory"
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    HTML = "html"
    CSS = "css"
    JSON = "json"
    MARKDOWN = "markdown"
    TEXT = "text"
    IMAGE = "image"
    BINARY = "binary"
    CONFIG = "config"
    UNKNOWN = "unknown"


@dataclass
class FileInfo:
    """File information with metadata."""
    path: Path
    name: str
    file_type: FileType
    size: int = 0
    modified: float = 0
    is_directory: bool = False
    is_hidden: bool = False
    permissions: str = ""
    children: List['FileInfo'] = field(default_factory=list)


class FileExplorer:
    """
    Modern file explorer with tree view and file operations.
    
    Features:
    - Beautiful tree view with icons
    - File type detection and icons
    - File operations (create, delete, rename)
    - Search and filtering
    - Modern styling with themes
    - Git integration (show git status)
    """
    
    def __init__(
        self,
        root_path: str,
        theme: str = "dark",
        show_hidden: bool = False,
        show_git_status: bool = True
    ):
        """
        Initialize the file explorer.
        
        Args:
            root_path: Root directory path
            theme: UI theme
            show_hidden: Whether to show hidden files
            show_git_status: Whether to show git status
        """
        self.root_path = Path(root_path).resolve()
        self.theme = theme
        self.show_hidden = show_hidden
        self.show_git_status = show_git_status
        
        self.console = Console() if RICH_AVAILABLE else None
        
        # File type mappings
        self.file_type_map = {
            '.py': FileType.PYTHON,
            '.js': FileType.JAVASCRIPT,
            '.ts': FileType.TYPESCRIPT,
            '.tsx': FileType.TYPESCRIPT,
            '.jsx': FileType.JAVASCRIPT,
            '.html': FileType.HTML,
            '.htm': FileType.HTML,
            '.css': FileType.CSS,
            '.scss': FileType.CSS,
            '.sass': FileType.CSS,
            '.json': FileType.JSON,
            '.md': FileType.MARKDOWN,
            '.txt': FileType.TEXT,
            '.png': FileType.IMAGE,
            '.jpg': FileType.IMAGE,
            '.jpeg': FileType.IMAGE,
            '.gif': FileType.IMAGE,
            '.svg': FileType.IMAGE,
            '.toml': FileType.CONFIG,
            '.yaml': FileType.CONFIG,
            '.yml': FileType.CONFIG,
            '.ini': FileType.CONFIG,
            '.cfg': FileType.CONFIG,
        }
        
        # File type icons
        self.file_icons = {
            FileType.DIRECTORY: "📁",
            FileType.PYTHON: "🐍",
            FileType.JAVASCRIPT: "🟨",
            FileType.TYPESCRIPT: "🔷",
            FileType.HTML: "🌐",
            FileType.CSS: "🎨",
            FileType.JSON: "📋",
            FileType.MARKDOWN: "📝",
            FileType.TEXT: "📄",
            FileType.IMAGE: "🖼️",
            FileType.CONFIG: "⚙️",
            FileType.BINARY: "📦",
            FileType.UNKNOWN: "❓"
        }
        
        # Git status icons
        self.git_status_icons = {
            'M': "📝",  # Modified
            'A': "➕",  # Added
            'D': "➖",  # Deleted
            'R': "🔄",  # Renamed
            'C': "📋",  # Copied
            'U': "❓",  # Unmerged
            '?': "❔",  # Untracked
            '!': "🚫"   # Ignored
        }
        
        # Theme colors
        self._setup_theme_colors()
        
        # Cache for file info
        self._file_cache: Dict[str, FileInfo] = {}
        self._git_status_cache: Dict[str, str] = {}
        
        # Load initial file tree
        self.refresh()
    
    def _setup_theme_colors(self) -> None:
        """Setup theme-specific colors."""
        if self.theme == "dark":
            self.colors = {
                "directory": "#58a6ff",
                "file": "#f0f6fc",
                "hidden": "#6b7280",
                "modified": "#fbbf24",
                "added": "#34d399",
                "deleted": "#f87171",
                "border": "#30363d"
            }
        elif self.theme == "light":
            self.colors = {
                "directory": "#0969da",
                "file": "#24292f",
                "hidden": "#656d76",
                "modified": "#bf8700",
                "added": "#1a7f37",
                "deleted": "#d1242f",
                "border": "#d0d7de"
            }
        else:  # claude theme
            self.colors = {
                "directory": "#2563eb",
                "file": "#1e293b",
                "hidden": "#64748b",
                "modified": "#d97706",
                "added": "#059669",
                "deleted": "#dc2626",
                "border": "#e2e8f0"
            }
    
    def _get_file_type(self, path: Path) -> FileType:
        """Get file type based on extension."""
        if path.is_dir():
            return FileType.DIRECTORY
        
        suffix = path.suffix.lower()
        return self.file_type_map.get(suffix, FileType.UNKNOWN)
    
    def _get_file_info(self, path: Path) -> FileInfo:
        """Get detailed file information."""
        try:
            stat = path.stat()
            file_type = self._get_file_type(path)
            
            return FileInfo(
                path=path,
                name=path.name,
                file_type=file_type,
                size=stat.st_size if not path.is_dir() else 0,
                modified=stat.st_mtime,
                is_directory=path.is_dir(),
                is_hidden=path.name.startswith('.'),
                permissions=oct(stat.st_mode)[-3:]
            )
        except (OSError, PermissionError) as e:
            logger.warning(f"Could not get info for {path}: {e}")
            return FileInfo(
                path=path,
                name=path.name,
                file_type=FileType.UNKNOWN,
                is_directory=path.is_dir(),
                is_hidden=path.name.startswith('.')
            )
    
    def _scan_directory(self, directory: Path, max_depth: int = 3, current_depth: int = 0) -> List[FileInfo]:
        """Scan directory and return file information."""
        if current_depth >= max_depth:
            return []
        
        files = []
        try:
            for item in sorted(directory.iterdir(), key=lambda x: (not x.is_dir(), x.name.lower())):
                # Skip hidden files if not showing them
                if not self.show_hidden and item.name.startswith('.'):
                    continue
                
                file_info = self._get_file_info(item)
                
                # Recursively scan subdirectories
                if file_info.is_directory and current_depth < max_depth - 1:
                    file_info.children = self._scan_directory(item, max_depth, current_depth + 1)
                
                files.append(file_info)
                
        except (OSError, PermissionError) as e:
            logger.warning(f"Could not scan directory {directory}: {e}")
        
        return files
    
    def _get_git_status(self) -> Dict[str, str]:
        """Get git status for files."""
        if not self.show_git_status:
            return {}
        
        try:
            import subprocess
            result = subprocess.run(
                ['git', 'status', '--porcelain'],
                cwd=self.root_path,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                status_map = {}
                for line in result.stdout.strip().split('\n'):
                    if line:
                        status = line[:2]
                        filepath = line[3:]
                        status_map[filepath] = status.strip()
                return status_map
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            pass
        
        return {}
    
    def refresh(self) -> None:
        """Refresh the file tree."""
        self._file_cache.clear()
        self._git_status_cache = self._get_git_status()
        
        # Scan root directory
        self.file_tree = self._scan_directory(self.root_path)
    
    def render_tree(self) -> Tree:
        """
        Render the file tree.
        
        Returns:
            Rich Tree widget
        """
        if not RICH_AVAILABLE:
            return self._render_text_tree()
        
        # Create root tree
        tree = Tree(
            f"📁 {self.root_path.name}",
            style=self.colors["directory"],
            guide_style=self.colors["border"]
        )
        
        # Add files to tree
        self._add_files_to_tree(tree, self.file_tree)
        
        return tree
    
    def _add_files_to_tree(self, parent_tree: Tree, files: List[FileInfo]) -> None:
        """Add files to a tree node."""
        for file_info in files:
            # Get icon and color
            icon = self.file_icons.get(file_info.file_type, "❓")
            
            if file_info.is_hidden:
                color = self.colors["hidden"]
            elif file_info.is_directory:
                color = self.colors["directory"]
            else:
                color = self.colors["file"]
            
            # Get git status
            git_icon = ""
            relative_path = str(file_info.path.relative_to(self.root_path))
            if relative_path in self._git_status_cache:
                status = self._git_status_cache[relative_path]
                git_icon = self.git_status_icons.get(status[0], "") + " "
            
            # Create label
            label = f"{git_icon}{icon} {file_info.name}"
            
            # Add size for files
            if not file_info.is_directory and file_info.size > 0:
                size_str = decimal(file_info.size)
                label += f" [dim]({size_str})[/dim]"
            
            # Add to tree
            node = parent_tree.add(label, style=color)
            
            # Add children if directory
            if file_info.children:
                self._add_files_to_tree(node, file_info.children)
    
    def _render_text_tree(self) -> str:
        """Render a simple text tree for fallback."""
        lines = [f"📁 {self.root_path.name}"]
        self._add_text_files(lines, self.file_tree, "")
        return "\n".join(lines)
    
    def _add_text_files(self, lines: List[str], files: List[FileInfo], prefix: str) -> None:
        """Add files to text tree."""
        for i, file_info in enumerate(files):
            is_last = i == len(files) - 1
            current_prefix = "└── " if is_last else "├── "
            next_prefix = "    " if is_last else "│   "
            
            icon = self.file_icons.get(file_info.file_type, "❓")
            lines.append(f"{prefix}{current_prefix}{icon} {file_info.name}")
            
            if file_info.children:
                self._add_text_files(lines, file_info.children, prefix + next_prefix)
    
    def render_panel(self) -> Panel:
        """
        Render the file explorer as a panel.
        
        Returns:
            Rich Panel with file tree
        """
        if not RICH_AVAILABLE:
            return f"File Explorer: {self.root_path}\n{self._render_text_tree()}"
        
        tree = self.render_tree()
        
        return Panel(
            tree,
            title="📁 File Explorer",
            title_align="left",
            border_style=self.colors["border"],
            padding=(1, 1)
        )
    
    def search_files(self, pattern: str) -> List[FileInfo]:
        """
        Search for files matching a pattern.
        
        Args:
            pattern: Search pattern (supports wildcards)
        
        Returns:
            List of matching files
        """
        import fnmatch
        
        matches = []
        
        def search_recursive(files: List[FileInfo]):
            for file_info in files:
                if fnmatch.fnmatch(file_info.name.lower(), pattern.lower()):
                    matches.append(file_info)
                
                if file_info.children:
                    search_recursive(file_info.children)
        
        search_recursive(self.file_tree)
        return matches
    
    def get_file_by_path(self, path: str) -> Optional[FileInfo]:
        """Get file info by path."""
        target_path = Path(path).resolve()
        
        def find_recursive(files: List[FileInfo]) -> Optional[FileInfo]:
            for file_info in files:
                if file_info.path == target_path:
                    return file_info
                
                if file_info.children:
                    result = find_recursive(file_info.children)
                    if result:
                        return result
            
            return None
        
        return find_recursive(self.file_tree)
    
    def create_file(self, path: str, content: str = "") -> bool:
        """Create a new file."""
        try:
            file_path = self.root_path / path
            file_path.parent.mkdir(parents=True, exist_ok=True)
            file_path.write_text(content)
            self.refresh()
            return True
        except Exception as e:
            logger.error(f"Failed to create file {path}: {e}")
            return False
    
    def create_directory(self, path: str) -> bool:
        """Create a new directory."""
        try:
            dir_path = self.root_path / path
            dir_path.mkdir(parents=True, exist_ok=True)
            self.refresh()
            return True
        except Exception as e:
            logger.error(f"Failed to create directory {path}: {e}")
            return False
    
    def delete_file(self, path: str) -> bool:
        """Delete a file or directory."""
        try:
            target_path = self.root_path / path
            if target_path.is_dir():
                import shutil
                shutil.rmtree(target_path)
            else:
                target_path.unlink()
            self.refresh()
            return True
        except Exception as e:
            logger.error(f"Failed to delete {path}: {e}")
            return False
    
    def rename_file(self, old_path: str, new_name: str) -> bool:
        """Rename a file or directory."""
        try:
            old_file_path = self.root_path / old_path
            new_file_path = old_file_path.parent / new_name
            old_file_path.rename(new_file_path)
            self.refresh()
            return True
        except Exception as e:
            logger.error(f"Failed to rename {old_path} to {new_name}: {e}")
            return False
