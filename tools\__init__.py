"""
Tools system for the AI agent.

This module provides a comprehensive tool system that allows the AI agent
to interact with the environment, execute code, browse the web, and more.
"""

import logging
import threading
from typing import List, Dict, Any, Optional, Generator, Callable
from enum import Enum

from tools.base import ToolSpec, ToolUse, Parameter, ConfirmFunc
from tools.registry import ToolRegistry, get_registry, register_tool
from message import Message
from config import get_config

logger = logging.getLogger(__name__)


class ToolFormat(Enum):
    """Tool invocation formats."""
    MARKDOWN = "markdown"
    XML = "xml"
    FUNCTION = "function"


# Global tool format
_tool_format: ToolFormat = ToolFormat.MARKDOWN

# Thread-local storage for tools
_thread_local = threading.local()


def get_tool_format() -> ToolFormat:
    """Get the current tool format."""
    return _tool_format


def set_tool_format(format: ToolFormat) -> None:
    """Set the tool format."""
    global _tool_format
    _tool_format = format


def _get_registry() -> ToolRegistry:
    """Get the global tool registry."""
    return get_registry()


def init_tools(allowlist: Optional[List[str]] = None) -> List[ToolSpec]:
    """
    Initialize tools with optional allowlist.
    
    Args:
        allowlist: List of tool names to allow (None for all available)
    
    Returns:
        List of initialized tools
    """
    registry = _get_registry()
    config = get_config()
    
    # Use allowlist from config if not provided
    if allowlist is None:
        allowlist = config.tools.enabled
    
    # Discover and register tools
    registry.discover_tools()
    
    # Initialize allowed tools
    initialized_tools = []
    for tool_name in allowlist:
        try:
            tool = registry.get_tool(tool_name)
            if tool and tool.is_available():
                if tool.init:
                    tool = tool.init()
                initialized_tools.append(tool)
                logger.debug(f"Initialized tool: {tool_name}")
            else:
                logger.warning(f"Tool '{tool_name}' is not available")
        except Exception as e:
            logger.error(f"Failed to get tool '{tool_name}': {e}")
    
    return initialized_tools


def get_tools() -> List[ToolSpec]:
    """Get all initialized tools."""
    registry = _get_registry()
    return registry.get_initialized_tools()


def get_tool(name: str) -> Optional[ToolSpec]:
    """Get a tool by name."""
    registry = _get_registry()
    return registry.get_tool(name)


def has_tool(name: str) -> bool:
    """Check if a tool is available."""
    return get_tool(name) is not None


def list_available_tools() -> List[str]:
    """List all available tool names."""
    registry = _get_registry()
    return registry.list_available_tools()


def execute_msg(
    message: Message,
    confirm: ConfirmFunc
) -> Generator[Message, None, None]:
    """
    Execute tools in a message and yield responses.
    
    Args:
        message: Message containing tool calls
        confirm: Confirmation function for tool execution
    
    Yields:
        Response messages from tool execution
    """
    if message.role != "assistant":
        logger.warning("Only assistant messages can contain tool calls")
        return
    
    # Extract tool uses from message content
    tool_uses = list(ToolUse.iter_from_content(message.content))
    
    for tool_use in tool_uses:
        if not tool_use.is_runnable:
            continue
        
        try:
            # Get the tool
            tool = get_tool(tool_use.tool)
            if not tool:
                yield Message(
                    role="system",
                    content=f"Error: Tool '{tool_use.tool}' not found",
                    call_id=tool_use.call_id
                )
                continue
            
            # Confirm execution if needed
            if not confirm(f"Execute {tool_use.tool} with: {tool_use.content}"):
                yield Message(
                    role="system",
                    content=f"Tool execution cancelled: {tool_use.tool}",
                    call_id=tool_use.call_id
                )
                continue
            
            # Execute the tool
            logger.info(f"Executing tool: {tool_use.tool}")
            
            for response in tool.execute(tool_use.content, **tool_use.kwargs):
                yield response.replace(call_id=tool_use.call_id)
                
        except KeyboardInterrupt:
            yield Message(
                role="system",
                content="Tool execution interrupted",
                call_id=tool_use.call_id
            )
            break
        except Exception as e:
            logger.error(f"Error executing tool '{tool_use.tool}': {e}")
            yield Message(
                role="system",
                content=f"Error executing {tool_use.tool}: {str(e)}",
                call_id=tool_use.call_id
            )


def get_tool_for_language(language: str) -> Optional[ToolSpec]:
    """Get the tool that handles a specific language/block type."""
    for tool in get_tools():
        if language in tool.block_types:
            return tool
    return None


def is_supported_language(language: str) -> bool:
    """Check if a language/block type is supported."""
    return get_tool_for_language(language) is not None


def clear_tools() -> None:
    """Clear all tools (useful for testing)."""
    if hasattr(_thread_local, "registry"):
        _thread_local.registry.clear()


def get_tools_for_llm() -> List[Dict[str, Any]]:
    """
    Get tools formatted for LLM function calling.
    
    Returns:
        List of tool definitions in OpenAI function calling format
    """
    tools = []
    
    for tool in get_tools():
        if not tool.is_runnable:
            continue
        
        # Convert to OpenAI function calling format
        function_def = {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
        
        # Add parameters
        for param in tool.parameters:
            function_def["function"]["parameters"]["properties"][param.name] = {
                "type": param.type,
                "description": param.description
            }
            
            if param.required:
                function_def["function"]["parameters"]["required"].append(param.name)
        
        tools.append(function_def)
    
    return tools


# Import all tool modules to register them
try:
    from tools import (
        shell,
        python,
        file_ops,
        browser,
        computer,
        github_tool,
        rag,
        web_tools,
        codebase_tools,
        memory_tool,
        process_manager,
    )
except ImportError as e:
    import logging
    logging.warning(f"Some tools could not be imported: {e}")
    # Import what we can
    try:
        from tools import shell
    except ImportError:
        pass
    try:
        from tools import python
    except ImportError:
        pass
    try:
        from tools import file_ops
    except ImportError:
        pass
    try:
        from tools import browser
    except ImportError:
        pass
    try:
        from tools import computer
    except ImportError:
        pass
    try:
        from tools import github_tool
    except ImportError:
        pass
    try:
        from tools import rag
    except ImportError:
        pass
    try:
        from tools import web_tools
    except ImportError:
        pass
    try:
        from tools import codebase_tools
    except ImportError:
        pass
    try:
        from tools import memory_tool
    except ImportError:
        pass
    try:
        from tools import process_manager
    except ImportError:
        pass

__all__ = [
    # Core classes
    "ToolSpec",
    "ToolUse", 
    "Parameter",
    "ConfirmFunc",
    "ToolFormat",
    "ToolRegistry",
    
    # Functions
    "get_tool_format",
    "set_tool_format",
    "init_tools",
    "get_tools",
    "get_tool",
    "has_tool",
    "list_available_tools",
    "execute_msg",
    "get_tool_for_language",
    "is_supported_language",
    "clear_tools",
    "get_tools_for_llm",
]
