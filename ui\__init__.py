"""
Modern UI System for Enhanced AI Coding Agent.

This module provides a comprehensive modern terminal interface with:
- Beautiful, clean design similar to claudecode
- Real-time chat interface with syntax highlighting
- Project file explorer with tree view
- Integrated terminal and code editor
- Modern responsive layout
"""

from typing import Optional, Dict, Any, List
import logging

logger = logging.getLogger(__name__)

# UI Components
from ui.modern_terminal import ModernTerminalInterface
from ui.chat_interface import ChatInterface
from ui.file_explorer import FileExplorer
from ui.code_editor import CodeEditor
from ui.syntax_highlighter import Syntax<PERSON>ighlighter

__all__ = [
    "ModernTerminalInterface",
    "ChatInterface", 
    "FileExplorer",
    "CodeEditor",
    "SyntaxHighlighter",
    "create_modern_ui",
    "launch_ui"
]


def create_modern_ui(
    workspace_path: Optional[str] = None,
    theme: str = "dark",
    layout: str = "split"
) -> ModernTerminalInterface:
    """
    Create a modern terminal interface instance.
    
    Args:
        workspace_path: Path to the workspace directory
        theme: UI theme ('dark', 'light', 'auto')
        layout: Layout mode ('split', 'tabbed', 'minimal')
    
    Returns:
        Configured ModernTerminalInterface instance
    """
    return ModernTerminalInterface(
        workspace_path=workspace_path,
        theme=theme,
        layout=layout
    )


def launch_ui(
    workspace_path: Optional[str] = None,
    host: str = "localhost",
    port: int = 8080,
    **kwargs
) -> None:
    """
    Launch the modern UI interface.
    
    Args:
        workspace_path: Path to the workspace directory
        host: Host to bind the server to
        port: Port to bind the server to
        **kwargs: Additional configuration options
    """
    ui = create_modern_ui(workspace_path=workspace_path, **kwargs)
    ui.launch(host=host, port=port)
