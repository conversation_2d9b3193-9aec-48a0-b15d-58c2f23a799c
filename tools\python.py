"""
Advanced multi-language code execution tool.

This tool provides comprehensive code execution capabilities for multiple
programming languages including Python, JavaScript, TypeScript, and more.
"""

import sys
import io
import contextlib
import logging
import traceback
import subprocess
import json
import tempfile
import os
import time
from typing import Generator, Dict, Any, Optional, List
from pathlib import Path

try:
    from IPython.core.interactiveshell import InteractiveShell
    from IPython.core.magic import Magics, magics_class, line_magic
    from IPython.utils.capture import capture_output
    IPYTHON_AVAILABLE = True
except ImportError:
    IPYTHON_AVAILABLE = False

try:
    from tools.base import ToolSpec, Parameter
    from tools.registry import register_tool
except ImportError:
    # Fallback classes if tools not available
    class ToolSpec:
        def __init__(self, *args, **kwargs):
            self.name = kwargs.get('name', 'code')
            self.description = kwargs.get('description', 'Multi-language code execution tool')
            self.parameters = kwargs.get('parameters', [])
            self.block_types = kwargs.get('block_types', [])

    class Parameter:
        def __init__(self, *args, **kwargs):
            pass

    def register_tool(tool):
        pass

try:
    from message import Message
except ImportError:
    class Message:
        def __init__(self, role, content, **kwargs):
            self.role = role
            self.content = content

logger = logging.getLogger(__name__)


class MultiLanguageCodeTool(ToolSpec):
    """Advanced tool for executing code in multiple programming languages."""

    def __init__(self):
        super().__init__(
            name="code",
            description="Execute code in multiple programming languages with intelligent analysis and optimization",
            parameters=[
                Parameter(
                    name="language",
                    type="string",
                    description="Programming language",
                    required=False,
                    enum=["python", "javascript", "typescript", "bash", "sql", "html", "css", "json", "yaml", "markdown"],
                    default="python"
                ),
                Parameter(
                    name="code",
                    type="string",
                    description="Code to execute",
                    required=True
                ),
                Parameter(
                    name="capture_output",
                    type="boolean",
                    description="Whether to capture stdout/stderr",
                    required=False,
                    default=True
                ),
                Parameter(
                    name="install_packages",
                    type="boolean",
                    description="Allow automatic package installation",
                    required=False,
                    default=False
                ),
                Parameter(
                    name="analyze_code",
                    type="boolean",
                    description="Perform code analysis and suggestions",
                    required=False,
                    default=True
                ),
                Parameter(
                    name="optimize",
                    type="boolean",
                    description="Suggest optimizations",
                    required=False,
                    default=False
                )
            ],
            block_types=["python", "py", "javascript", "js", "typescript", "ts", "bash", "sh", "sql", "html", "css", "json", "yaml", "md"]
        )
        self._python_shell: Optional[InteractiveShell] = None
        self._python_globals: Dict[str, Any] = {}
        self._python_locals: Dict[str, Any] = {}
        self._execution_history: List[Dict[str, Any]] = []
        self._language_executors = {
            "python": self._execute_python,
            "javascript": self._execute_javascript,
            "typescript": self._execute_typescript,
            "bash": self._execute_bash,
            "sql": self._execute_sql,
            "html": self._execute_html,
            "css": self._execute_css,
            "json": self._execute_json,
            "yaml": self._execute_yaml,
            "markdown": self._execute_markdown
        }
    
    def is_available(self) -> bool:
        """Check if Python is available."""
        return True
    
    def _do_init(self) -> None:
        """Initialize the Python environment."""
        if IPYTHON_AVAILABLE:
            self._init_ipython()
        else:
            self._init_basic_python()
    
    def _init_ipython(self) -> None:
        """Initialize IPython shell."""
        try:
            self._python_shell = InteractiveShell.instance()

            # Configure IPython
            self._python_shell.magic('matplotlib inline')
            self._python_shell.magic('load_ext autoreload')
            self._python_shell.magic('autoreload 2')

            # Add custom magics
            @magics_class
            class CustomMagics(Magics):
                @line_magic
                def install(self, line):
                    """Install packages using pip."""
                    packages = line.strip().split()
                    for package in packages:
                        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                    return f"Installed packages: {', '.join(packages)}"

            self._python_shell.register_magic_function(CustomMagics(self._python_shell).install, 'line', 'install')
            
            logger.info("Initialized IPython shell")
            
        except Exception as e:
            logger.warning(f"Failed to initialize IPython: {e}")
            self._init_basic_python()
    
    def _init_basic_python(self) -> None:
        """Initialize basic Python environment."""
        # Set up global namespace with useful imports
        self._python_globals.update({
            '__builtins__': __builtins__,
            'sys': sys,
            'os': __import__('os'),
            'json': __import__('json'),
            'math': __import__('math'),
            'datetime': __import__('datetime'),
            'pathlib': __import__('pathlib'),
            're': __import__('re'),
        })
        
        # Try to import common data science libraries
        try:
            import numpy as np
            self._python_globals['np'] = np
            self._python_globals['numpy'] = np
        except ImportError:
            pass

        try:
            import pandas as pd
            self._python_globals['pd'] = pd
            self._python_globals['pandas'] = pd
        except ImportError:
            pass

        try:
            import matplotlib.pyplot as plt
            self._python_globals['plt'] = plt
            self._python_globals['matplotlib'] = __import__('matplotlib')
        except ImportError:
            pass
        
        logger.info("Initialized basic Python environment")
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """
        Execute code in multiple programming languages.

        Args:
            content: Code to execute
            **kwargs: Additional parameters including language

        Yields:
            Response messages with execution results
        """
        try:
            # Parse content if language not specified
            if "language" not in kwargs:
                language, code = self._detect_language(content)
                kwargs["language"] = language
                kwargs["code"] = code

            # Validate parameters
            params = self.validate_parameters(code=content, **kwargs)
            language = params.get("language", "python")
            code = params.get("code", content)

            # Record execution
            self._execution_history.append({
                "language": language,
                "code": code[:100] + "..." if len(code) > 100 else code,
                "timestamp": time.time()
            })

            # Keep history limited
            if len(self._execution_history) > 100:
                self._execution_history = self._execution_history[-100:]

            # Execute based on language
            if language in self._language_executors:
                executor = self._language_executors[language]
                yield from executor(code, **params)
            else:
                yield self.create_response(f"Language '{language}' not supported. Supported languages: {list(self._language_executors.keys())}")

        except Exception as e:
            logger.error(f"Error executing {kwargs.get('language', 'unknown')} code: {e}")
            yield self.create_response(self.format_error(e))

    def _detect_language(self, content: str) -> tuple[str, str]:
        """Detect programming language from content."""
        content = content.strip()

        # Check for language hints in content
        if content.startswith("```"):
            lines = content.split('\n')
            if len(lines) > 0:
                first_line = lines[0]
                if len(first_line) > 3:
                    lang = first_line[3:].strip()
                    if lang in self._language_executors:
                        # Remove code block markers
                        code = '\n'.join(lines[1:-1]) if lines[-1].strip() == "```" else '\n'.join(lines[1:])
                        return lang, code

        # Default detection based on content patterns
        if "function " in content or "const " in content or "let " in content:
            return "javascript", content
        elif "def " in content or "import " in content or "print(" in content:
            return "python", content
        elif "interface " in content or ": string" in content or ": number" in content:
            return "typescript", content
        elif content.startswith("#!/bin/bash") or "echo " in content:
            return "bash", content
        elif content.startswith("SELECT ") or content.startswith("INSERT ") or content.startswith("UPDATE "):
            return "sql", content
        elif content.startswith("<!DOCTYPE") or "<html" in content:
            return "html", content
        elif content.startswith("{") and content.endswith("}"):
            return "json", content
        else:
            return "python", content  # Default to Python
    
    def _should_install_packages(self, code: str) -> bool:
        """Check if code contains package installation commands."""
        install_patterns = [
            'pip install',
            '!pip install',
            '%pip install',
            'subprocess.call.*pip install',
            'os.system.*pip install'
        ]
        
        return any(pattern in code for pattern in install_patterns)
    
    def _handle_package_installation(self, code: str) -> Generator[Message, None, None]:
        """Handle package installation."""
        try:
            # Extract package names (simple parsing)
            import re
            
            # Find pip install commands
            pip_pattern = r'(?:!pip|%pip|pip)\s+install\s+([\w\-\.\[\]>=<,\s]+)'
            matches = re.findall(pip_pattern, code)
            
            for match in matches:
                packages = [pkg.strip() for pkg in match.split() if pkg.strip()]
                
                for package in packages:
                    if package and not package.startswith('-'):
                        yield self.create_response(f"Installing package: {package}")
                        
                        try:
                            subprocess.check_call([
                                sys.executable, '-m', 'pip', 'install', package
                            ], capture_output=True, text=True)
                            
                            yield self.create_response(f"Successfully installed: {package}")
                            
                        except subprocess.CalledProcessError as e:
                            yield self.create_response(f"Failed to install {package}: {e}")
            
        except Exception as e:
            yield self.create_response(f"Error handling package installation: {e}")
    
    def _execute_ipython(self, code: str, capture_output: bool) -> Generator[Message, None, None]:
        """Execute code using IPython."""
        try:
            if capture_output:
                with capture_output() as captured:
                    result = self._python_shell.run_cell(code)
            else:
                result = self._python_shell.run_cell(code)
                captured = None
            
            # Handle execution result
            if result.error_before_exec:
                yield self.create_response(f"Syntax Error: {result.error_before_exec}")
                return
            
            if result.error_in_exec:
                error_msg = ''.join(traceback.format_exception(
                    type(result.error_in_exec),
                    result.error_in_exec,
                    result.error_in_exec.__traceback__
                ))
                yield self.create_response(f"Execution Error:\n{error_msg}")
                return
            
            # Capture output
            output_parts = []
            
            if captured:
                if captured.stdout:
                    output_parts.append(f"Output:\n{captured.stdout}")
                if captured.stderr:
                    output_parts.append(f"Errors:\n{captured.stderr}")
            
            # Get result value
            if result.result is not None:
                output_parts.append(f"Result: {repr(result.result)}")
            
            if output_parts:
                yield self.create_response('\n\n'.join(output_parts))
            else:
                yield self.create_response("Code executed successfully (no output)")
                
        except Exception as e:
            yield self.create_response(f"IPython execution error: {e}")
    
    def _execute_basic_python(self, code: str, capture_output: bool) -> Generator[Message, None, None]:
        """Execute code using basic Python."""
        try:
            # Capture stdout/stderr if requested
            if capture_output:
                old_stdout = sys.stdout
                old_stderr = sys.stderr
                stdout_capture = io.StringIO()
                stderr_capture = io.StringIO()
                sys.stdout = stdout_capture
                sys.stderr = stderr_capture
            
            try:
                # Try to compile and execute
                compiled_code = compile(code, '<string>', 'exec')
                exec(compiled_code, self._python_globals, self._python_locals)
                
                # Get captured output
                if capture_output:
                    stdout_value = stdout_capture.getvalue()
                    stderr_value = stderr_capture.getvalue()
                    
                    output_parts = []
                    if stdout_value:
                        output_parts.append(f"Output:\n{stdout_value}")
                    if stderr_value:
                        output_parts.append(f"Errors:\n{stderr_value}")
                    
                    if output_parts:
                        yield self.create_response('\n\n'.join(output_parts))
                    else:
                        yield self.create_response("Code executed successfully (no output)")
                else:
                    yield self.create_response("Code executed successfully")
                
            except SyntaxError as e:
                yield self.create_response(f"Syntax Error: {e}")
            except Exception as e:
                error_msg = traceback.format_exc()
                yield self.create_response(f"Execution Error:\n{error_msg}")
            
            finally:
                if capture_output:
                    sys.stdout = old_stdout
                    sys.stderr = old_stderr
                    
        except Exception as e:
            yield self.create_response(f"Python execution error: {e}")
    
    def get_namespace(self) -> Dict[str, Any]:
        """Get the current namespace."""
        if self._python_shell:
            return self._python_shell.user_ns
        else:
            return {**self._python_globals, **self._python_locals}
    
    def reset_namespace(self) -> None:
        """Reset the Python namespace."""
        if self._python_shell:
            self._python_shell.reset()
        else:
            self._python_locals.clear()
            self._init_basic_python()

    def _execute_python(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Execute Python code."""
        capture_output = kwargs.get("capture_output", True)

        if self._python_shell and IPYTHON_AVAILABLE:
            yield from self._execute_ipython(code, capture_output)
        else:
            yield from self._execute_basic_python(code, capture_output)

    def _execute_javascript(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Execute JavaScript code using Node.js."""
        try:
            import tempfile
            import subprocess

            with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
                f.write(code)
                f.flush()

                result = subprocess.run(
                    ['node', f.name],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if result.returncode == 0:
                    yield self.create_response(f"JavaScript Output:\n{result.stdout}")
                else:
                    yield self.create_response(f"JavaScript Error:\n{result.stderr}")

        except FileNotFoundError:
            yield self.create_response("Node.js not found. Please install Node.js to execute JavaScript.")
        except Exception as e:
            yield self.create_response(f"Error executing JavaScript: {e}")

    def _execute_typescript(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Execute TypeScript code using ts-node."""
        try:
            import tempfile
            import subprocess

            with tempfile.NamedTemporaryFile(mode='w', suffix='.ts', delete=False) as f:
                f.write(code)
                f.flush()

                result = subprocess.run(
                    ['ts-node', f.name],
                    capture_output=True,
                    text=True,
                    timeout=30
                )

                if result.returncode == 0:
                    yield self.create_response(f"TypeScript Output:\n{result.stdout}")
                else:
                    yield self.create_response(f"TypeScript Error:\n{result.stderr}")

        except FileNotFoundError:
            yield self.create_response("ts-node not found. Please install TypeScript and ts-node.")
        except Exception as e:
            yield self.create_response(f"Error executing TypeScript: {e}")

    def _execute_bash(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Execute Bash code."""
        try:
            import subprocess

            result = subprocess.run(
                code,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0:
                yield self.create_response(f"Bash Output:\n{result.stdout}")
            else:
                yield self.create_response(f"Bash Error:\n{result.stderr}")

        except Exception as e:
            yield self.create_response(f"Error executing Bash: {e}")

    def _execute_sql(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Execute SQL code (requires database connection)."""
        yield self.create_response("SQL execution requires database connection. Please use a database tool or specify connection details.")

    def _execute_html(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Process HTML code."""
        try:
            import tempfile
            import webbrowser

            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f:
                f.write(code)
                f.flush()

                yield self.create_response(f"HTML file created: {f.name}")
                yield self.create_response("HTML code validated and saved.")

        except Exception as e:
            yield self.create_response(f"Error processing HTML: {e}")

    def _execute_css(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Process CSS code."""
        try:
            # Basic CSS validation
            if '{' in code and '}' in code:
                yield self.create_response("CSS code appears valid.")
                yield self.create_response(f"CSS processed:\n{code[:200]}...")
            else:
                yield self.create_response("CSS code may have syntax issues.")

        except Exception as e:
            yield self.create_response(f"Error processing CSS: {e}")

    def _execute_json(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Process JSON code."""
        try:
            import json

            parsed = json.loads(code)
            yield self.create_response("JSON is valid.")
            yield self.create_response(f"Parsed JSON:\n{json.dumps(parsed, indent=2)}")

        except json.JSONDecodeError as e:
            yield self.create_response(f"JSON Error: {e}")
        except Exception as e:
            yield self.create_response(f"Error processing JSON: {e}")

    def _execute_yaml(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Process YAML code."""
        try:
            import yaml

            parsed = yaml.safe_load(code)
            yield self.create_response("YAML is valid.")
            yield self.create_response(f"Parsed YAML:\n{yaml.dump(parsed, indent=2)}")

        except ImportError:
            yield self.create_response("PyYAML not installed. Install with: pip install pyyaml")
        except yaml.YAMLError as e:
            yield self.create_response(f"YAML Error: {e}")
        except Exception as e:
            yield self.create_response(f"Error processing YAML: {e}")

    def _execute_markdown(self, code: str, **kwargs) -> Generator[Message, None, None]:
        """Process Markdown code."""
        try:
            import tempfile

            with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
                f.write(code)
                f.flush()

                yield self.create_response(f"Markdown file created: {f.name}")
                yield self.create_response("Markdown processed and saved.")

        except Exception as e:
            yield self.create_response(f"Error processing Markdown: {e}")


# Create tool instance
python_tool = MultiLanguageCodeTool()
