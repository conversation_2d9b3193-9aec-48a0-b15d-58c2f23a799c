"""
Advanced Codebase Analyzer for Enhanced Code Understanding.

This tool provides comprehensive codebase analysis capabilities including:
- Advanced code analysis and indexing
- Multi-file coordination and relationship mapping
- Project-level reasoning and understanding
- Intelligent context retrieval
- Code pattern recognition and architectural analysis
"""

import os
import ast
import json
import logging
import hashlib
import threading
import time
from typing import Dict, Any, List, Optional, Set, Tuple, Generator
from dataclasses import dataclass, asdict, field
from pathlib import Path
from collections import defaultdict
from enum import Enum
import re

from tools.base import ToolSpec, Parameter
from tools.registry import register_tool
from message import Message

# Import large codebase optimizer
try:
    from large_codebase_optimizer import (
        LargeCodebaseOptimizer, OptimizationLevel, ProcessingStrategy,
        analyze_file_optimized, create_optimized_analyzer
    )
    OPTIMIZER_AVAILABLE = True
except ImportError:
    OPTIMIZER_AVAILABLE = False

logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """Types of codebase analysis."""
    ARCHITECTURE = "architecture"
    DEPENDENCIES = "dependencies"
    CODE_QUALITY = "code_quality"
    COMPLEXITY = "complexity"
    PATTERNS = "patterns"
    SECURITY = "security"
    PERFORMANCE = "performance"
    MAINTAINABILITY = "maintainability"
    MULTI_FILE_COORDINATION = "multi_file_coordination"
    PROJECT_REASONING = "project_reasoning"


class CodeElement(Enum):
    """Types of code elements."""
    CLASS = "class"
    FUNCTION = "function"
    METHOD = "method"
    VARIABLE = "variable"
    IMPORT = "import"
    MODULE = "module"
    PACKAGE = "package"


@dataclass
class CodeEntity:
    """Represents a code entity (class, function, variable, etc.)."""
    name: str
    entity_type: str  # 'class', 'function', 'variable', 'import', 'constant'
    file_path: str
    line_number: int
    end_line: Optional[int] = None
    signature: Optional[str] = None
    docstring: Optional[str] = None
    dependencies: List[str] = None
    complexity_score: float = 0.0
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class FileAnalysis:
    """Represents analysis results for a single file."""
    file_path: str
    language: str
    entities: List[CodeEntity]
    imports: List[str]
    exports: List[str]
    complexity_metrics: Dict[str, float]
    dependencies: List[str]
    size_metrics: Dict[str, int]
    last_modified: str
    analysis_timestamp: str


@dataclass
class ProjectAnalysis:
    """Represents comprehensive project analysis."""
    project_root: str
    files: Dict[str, FileAnalysis]
    dependency_graph: Dict[str, List[str]]
    architecture_patterns: List[str]
    technology_stack: List[str]
    complexity_summary: Dict[str, float]
    hotspots: List[str]  # Files that need attention
    recommendations: List[str]


class AdvancedCodebaseAnalyzer(ToolSpec):
    """
    Advanced codebase analyzer with intelligent code understanding capabilities.
    
    This tool provides:
    - Deep code analysis and indexing
    - Multi-file relationship mapping
    - Project-level architectural understanding
    - Intelligent context retrieval
    - Code quality and complexity analysis
    - Refactoring suggestions
    """
    
    def __init__(self):
        parameters = [
            Parameter(
                name="analysis_type",
                type="string",
                description="Type of analysis to perform",
                required=False,
                default="comprehensive",
                enum=["comprehensive", "quick", "focused", "dependency", "complexity", "architecture"]
            ),
            Parameter(
                name="target_path",
                type="string",
                description="Path to analyze (file or directory)",
                required=False,
                default="."
            ),
            Parameter(
                name="include_patterns",
                type="string",
                description="File patterns to include (comma-separated)",
                required=False,
                default="*.py,*.js,*.ts,*.java,*.cpp,*.h,*.cs,*.go,*.rs"
            ),
            Parameter(
                name="exclude_patterns",
                type="string",
                description="File patterns to exclude (comma-separated)",
                required=False,
                default="node_modules,__pycache__,.git,build,dist,target"
            ),
            Parameter(
                name="max_depth",
                type="integer",
                description="Maximum directory depth to analyze",
                required=False,
                default=10
            ),
            Parameter(
                name="focus_entity",
                type="string",
                description="Specific entity to focus analysis on (class, function, etc.)",
                required=False
            ),
            Parameter(
                name="include_metrics",
                type="boolean",
                description="Whether to include detailed complexity metrics",
                required=False,
                default=True
            ),
            Parameter(
                name="generate_recommendations",
                type="boolean",
                description="Whether to generate improvement recommendations",
                required=False,
                default=True
            )
        ]
        
        super().__init__(
            name="advanced_codebase_analyzer",
            description="Advanced codebase analyzer with intelligent code understanding, multi-file coordination, and project-level reasoning capabilities",
            parameters=parameters,
            block_types=["codebase_analyzer", "code_analysis", "project_analysis"]
        )
        
        # Language-specific analyzers
        self.language_analyzers = {
            ".py": self._analyze_python_file,
            ".js": self._analyze_javascript_file,
            ".ts": self._analyze_typescript_file,
            ".java": self._analyze_java_file,
            ".cpp": self._analyze_cpp_file,
            ".h": self._analyze_cpp_file,
            ".cs": self._analyze_csharp_file,
            ".go": self._analyze_go_file,
            ".rs": self._analyze_rust_file
        }
        
        # Architecture patterns to detect
        self.architecture_patterns = {
            "mvc": ["model", "view", "controller"],
            "mvp": ["model", "view", "presenter"],
            "mvvm": ["model", "view", "viewmodel"],
            "microservices": ["service", "api", "gateway"],
            "layered": ["controller", "service", "repository", "dao"],
            "hexagonal": ["port", "adapter", "domain"],
            "clean": ["entity", "usecase", "interface", "framework"]
        }

        # Large codebase optimizer
        self.optimizer = None
        self.optimization_enabled = OPTIMIZER_AVAILABLE

        # Analysis cache
        self.analysis_cache = {}
        self.cache_ttl = 3600  # 1 hour
    
    def is_available(self) -> bool:
        """Check if the tool is available."""
        return True
    
    def execute(self, content: str, **kwargs) -> Generator[Message, None, None]:
        """Execute the codebase analysis."""
        try:
            # Validate parameters
            params = self.validate_parameters(**kwargs)
            
            # Determine target path
            target_path = params.get("target_path", ".")
            if not os.path.exists(target_path):
                yield self.create_response(f"❌ Path not found: {target_path}")
                return
            
            yield self.create_response(f"🔍 Starting {params['analysis_type']} analysis of: {target_path}")
            
            # Perform analysis based on type
            if params["analysis_type"] == "comprehensive":
                yield from self._comprehensive_analysis(target_path, params)
            elif params["analysis_type"] == "quick":
                yield from self._quick_analysis(target_path, params)
            elif params["analysis_type"] == "focused":
                yield from self._focused_analysis(target_path, params)
            elif params["analysis_type"] == "dependency":
                yield from self._dependency_analysis(target_path, params)
            elif params["analysis_type"] == "complexity":
                yield from self._complexity_analysis(target_path, params)
            elif params["analysis_type"] == "architecture":
                yield from self._architecture_analysis(target_path, params)
            
        except Exception as e:
            logger.error(f"Error in codebase analysis: {e}")
            yield self.create_response(f"Error in codebase analysis: {str(e)}")
    
    def _comprehensive_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform comprehensive codebase analysis."""
        # Initialize optimizer for large codebases
        if self.optimization_enabled and OPTIMIZER_AVAILABLE:
            try:
                self.optimizer = create_optimized_analyzer(target_path)
                metrics = self.optimizer.analyze_codebase_size()

                yield self.create_response(
                    f"🔍 Codebase Analysis:\n"
                    f"Files: {metrics.total_files:,}\n"
                    f"Lines: {metrics.total_lines:,}\n"
                    f"Size: {metrics.total_size_bytes / 1024 / 1024:.1f} MB\n"
                    f"Complexity Score: {metrics.complexity_score:.2f}\n"
                    f"Estimated Processing Time: {metrics.estimated_processing_time:.1f}s"
                )

                # Use optimized processing for large codebases
                if metrics.total_files > 1000:
                    yield from self._optimized_comprehensive_analysis(target_path, params)
                    return
            except Exception as e:
                logger.warning(f"Optimizer initialization failed: {e}")

        # Fallback to standard analysis
        # Discover files
        files = self._discover_files(target_path, params)
        yield self.create_response(f"📁 Found {len(files)} files to analyze")

        # Analyze each file
        file_analyses = {}
        for i, file_path in enumerate(files):
            if i % 10 == 0:  # Progress update every 10 files
                yield self.create_response(f"📊 Analyzing file {i+1}/{len(files)}: {os.path.basename(file_path)}")

            analysis = self._analyze_file(file_path)
            if analysis:
                file_analyses[file_path] = analysis
        
        # Build project analysis
        project_analysis = self._build_project_analysis(target_path, file_analyses)
        
        # Generate comprehensive report
        report = self._generate_comprehensive_report(project_analysis)
        yield self.create_response(report)
        
        # Generate recommendations if requested
        if params["generate_recommendations"]:
            recommendations = self._generate_recommendations(project_analysis)
            yield self.create_response(f"## 💡 Recommendations\n\n{recommendations}")

    def _optimized_comprehensive_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform optimized comprehensive analysis for large codebases."""
        if not self.optimizer:
            yield self.create_response("❌ Optimizer not available")
            return

        yield self.create_response("🚀 Using optimized analysis for large codebase...")

        # Get optimization strategy
        opt_level, strategy = self.optimizer.get_optimization_strategy()
        yield self.create_response(f"📈 Optimization: {opt_level.value} codebase, {strategy.value} processing")

        # Prepare analysis function
        def analyze_file_wrapper(file_path: str):
            return analyze_file_optimized(file_path)

        # Process files with optimization
        file_analyses = {}

        try:
            processed_count = 0
            for result in self.optimizer.process_codebase_optimized(
                analysis_function=analyze_file_wrapper,
                incremental=True
            ):
                if result.success and result.analysis_data:
                    # Convert optimized result to our format
                    file_analysis = self._convert_optimized_result(result)
                    if file_analysis:
                        file_analyses[result.file_path] = file_analysis

                processed_count += 1
                if processed_count % 100 == 0:
                    yield self.create_response(f"📊 Processed {processed_count} files...")

        except Exception as e:
            yield self.create_response(f"⚠️ Optimization error: {e}")
            return

        yield self.create_response(f"✅ Optimized analysis complete: {len(file_analyses)} files analyzed")

        # Get performance stats
        stats = self.optimizer.get_performance_stats()
        yield self.create_response(
            f"📈 Performance Stats:\n"
            f"Cache Hit Rate: {stats['cache_hit_rate']:.1%}\n"
            f"Average Processing Time: {stats['average_processing_time']:.3f}s per file\n"
            f"Total Processing Time: {stats['total_processing_time']:.1f}s"
        )

        # Create project analysis
        project_analysis = ProjectAnalysis(
            project_path=target_path,
            files=file_analyses,
            total_files=len(file_analyses),
            total_lines=sum(fa.size_metrics.get("total_lines", 0) for fa in file_analyses.values()),
            languages=list(set(fa.language for fa in file_analyses.values())),
            analysis_timestamp=str(time.time())
        )

        # Generate comprehensive report
        report = self._generate_comprehensive_report(project_analysis)
        yield self.create_response(report)

        # Generate recommendations if requested
        if params["generate_recommendations"]:
            recommendations = self._generate_recommendations(project_analysis)
            yield self.create_response(f"## 💡 Recommendations\n\n{recommendations}")

    def _convert_optimized_result(self, result) -> Optional[FileAnalysis]:
        """Convert optimized analysis result to FileAnalysis format."""
        try:
            data = result.analysis_data
            if not data:
                return None

            # Convert to our FileAnalysis format
            entities = []

            # Convert functions
            for func in data.get("functions", []):
                entities.append(CodeEntity(
                    name=func["name"],
                    entity_type="function",
                    line_number=func.get("line", 0),
                    signature=f"{func['name']}({func.get('args', 0)} args)",
                    metadata={"args": func.get("args", 0)}
                ))

            # Convert classes
            for cls in data.get("classes", []):
                entities.append(CodeEntity(
                    name=cls["name"],
                    entity_type="class",
                    line_number=cls.get("line", 0),
                    signature=f"class {cls['name']}",
                    metadata={"methods": cls.get("methods", 0)}
                ))

            return FileAnalysis(
                file_path=result.file_path,
                language=data.get("language", "unknown"),
                entities=entities,
                imports=data.get("imports", []),
                exports=[],
                complexity_metrics={"lines_of_code": data.get("line_count", 0)},
                dependencies=data.get("imports", []),
                size_metrics={
                    "total_lines": data.get("line_count", 0),
                    "character_count": data.get("character_count", 0)
                },
                last_modified=str(time.time()),
                analysis_timestamp=str(time.time())
            )

        except Exception as e:
            logger.warning(f"Failed to convert optimized result: {e}")
            return None

    def _quick_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform quick analysis focusing on high-level metrics."""
        files = self._discover_files(target_path, params)
        
        # Quick metrics
        total_files = len(files)
        total_lines = 0
        language_distribution = defaultdict(int)
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = len(f.readlines())
                    total_lines += lines
                
                ext = Path(file_path).suffix.lower()
                language_distribution[ext] += 1
            except Exception:
                continue
        
        # Generate quick report
        report_parts = [
            "## 📊 Quick Analysis Results",
            f"**Total Files:** {total_files}",
            f"**Total Lines of Code:** {total_lines:,}",
            "",
            "### Language Distribution"
        ]
        
        for ext, count in sorted(language_distribution.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_files) * 100
            report_parts.append(f"- {ext or 'no extension'}: {count} files ({percentage:.1f}%)")
        
        yield self.create_response("\n".join(report_parts))
    
    def _focused_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform focused analysis on specific entity."""
        focus_entity = params.get("focus_entity")
        if not focus_entity:
            yield self.create_response("❌ focus_entity parameter required for focused analysis")
            return
        
        files = self._discover_files(target_path, params)
        matches = []
        
        for file_path in files:
            analysis = self._analyze_file(file_path)
            if analysis:
                for entity in analysis.entities:
                    if focus_entity.lower() in entity.name.lower():
                        matches.append((file_path, entity))
        
        if not matches:
            yield self.create_response(f"❌ No entities found matching: {focus_entity}")
            return
        
        # Generate focused report
        report_parts = [f"## 🎯 Focused Analysis: {focus_entity}", f"Found {len(matches)} matches:\n"]
        
        for file_path, entity in matches:
            report_parts.append(f"### {entity.name} ({entity.entity_type})")
            report_parts.append(f"**File:** {file_path}")
            report_parts.append(f"**Line:** {entity.line_number}")
            if entity.signature:
                report_parts.append(f"**Signature:** `{entity.signature}`")
            if entity.docstring:
                report_parts.append(f"**Documentation:** {entity.docstring[:200]}...")
            report_parts.append("")
        
        yield self.create_response("\n".join(report_parts))
    
    def _dependency_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform dependency analysis."""
        files = self._discover_files(target_path, params)
        dependency_graph = defaultdict(set)
        
        for file_path in files:
            analysis = self._analyze_file(file_path)
            if analysis:
                for dep in analysis.dependencies:
                    dependency_graph[file_path].add(dep)
        
        # Find circular dependencies
        circular_deps = self._find_circular_dependencies(dependency_graph)
        
        # Generate dependency report
        report_parts = [
            "## 🔗 Dependency Analysis",
            f"**Total Files:** {len(files)}",
            f"**Files with Dependencies:** {len(dependency_graph)}",
            f"**Circular Dependencies Found:** {len(circular_deps)}",
            ""
        ]
        
        if circular_deps:
            report_parts.append("### ⚠️ Circular Dependencies")
            for cycle in circular_deps[:5]:  # Show first 5 cycles
                report_parts.append(f"- {' → '.join(cycle)}")
            report_parts.append("")
        
        # Top dependencies
        all_deps = defaultdict(int)
        for deps in dependency_graph.values():
            for dep in deps:
                all_deps[dep] += 1
        
        if all_deps:
            report_parts.append("### 📊 Most Common Dependencies")
            for dep, count in sorted(all_deps.items(), key=lambda x: x[1], reverse=True)[:10]:
                report_parts.append(f"- {dep}: {count} files")
        
        yield self.create_response("\n".join(report_parts))
    
    def _complexity_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform complexity analysis."""
        files = self._discover_files(target_path, params)
        complexity_data = []
        
        for file_path in files:
            analysis = self._analyze_file(file_path)
            if analysis and analysis.complexity_metrics:
                complexity_data.append((file_path, analysis.complexity_metrics))
        
        if not complexity_data:
            yield self.create_response("❌ No complexity data available")
            return
        
        # Calculate overall metrics
        total_complexity = sum(metrics.get("cyclomatic", 0) for _, metrics in complexity_data)
        avg_complexity = total_complexity / len(complexity_data) if complexity_data else 0
        
        # Find high complexity files
        high_complexity = [(path, metrics) for path, metrics in complexity_data 
                          if metrics.get("cyclomatic", 0) > avg_complexity * 1.5]
        
        # Generate complexity report
        report_parts = [
            "## 📈 Complexity Analysis",
            f"**Files Analyzed:** {len(complexity_data)}",
            f"**Average Cyclomatic Complexity:** {avg_complexity:.2f}",
            f"**High Complexity Files:** {len(high_complexity)}",
            ""
        ]
        
        if high_complexity:
            report_parts.append("### ⚠️ High Complexity Files")
            for path, metrics in sorted(high_complexity, key=lambda x: x[1].get("cyclomatic", 0), reverse=True)[:10]:
                complexity = metrics.get("cyclomatic", 0)
                report_parts.append(f"- {os.path.basename(path)}: {complexity:.1f}")
        
        yield self.create_response("\n".join(report_parts))
    
    def _architecture_analysis(self, target_path: str, params: Dict[str, Any]) -> Generator[Message, None, None]:
        """Perform architectural pattern analysis."""
        files = self._discover_files(target_path, params)
        
        # Analyze directory structure
        directories = set()
        for file_path in files:
            directories.add(os.path.dirname(file_path))
        
        # Detect architecture patterns
        detected_patterns = []
        for pattern_name, keywords in self.architecture_patterns.items():
            score = 0
            for directory in directories:
                dir_name = os.path.basename(directory).lower()
                for keyword in keywords:
                    if keyword in dir_name:
                        score += 1
            
            if score >= len(keywords) * 0.5:  # At least 50% of keywords found
                detected_patterns.append((pattern_name, score))
        
        # Generate architecture report
        report_parts = [
            "## 🏗️ Architecture Analysis",
            f"**Directories Analyzed:** {len(directories)}",
            f"**Patterns Detected:** {len(detected_patterns)}",
            ""
        ]
        
        if detected_patterns:
            report_parts.append("### 🎯 Detected Patterns")
            for pattern, score in sorted(detected_patterns, key=lambda x: x[1], reverse=True):
                confidence = min(score / len(self.architecture_patterns[pattern]), 1.0) * 100
                report_parts.append(f"- {pattern.upper()}: {confidence:.0f}% confidence")
        else:
            report_parts.append("### 🤔 No Clear Architectural Patterns Detected")
            report_parts.append("Consider organizing code into clearer architectural patterns.")
        
        yield self.create_response("\n".join(report_parts))
    
    def _discover_files(self, target_path: str, params: Dict[str, Any]) -> List[str]:
        """Discover files to analyze based on patterns."""
        include_patterns = params.get("include_patterns", "").split(",")
        exclude_patterns = params.get("exclude_patterns", "").split(",")
        max_depth = params.get("max_depth", 10)
        
        files = []
        
        if os.path.isfile(target_path):
            return [target_path]
        
        for root, dirs, filenames in os.walk(target_path):
            # Check depth
            depth = root[len(target_path):].count(os.sep)
            if depth >= max_depth:
                dirs[:] = []  # Don't recurse deeper
                continue
            
            # Filter directories
            dirs[:] = [d for d in dirs if not any(pattern.strip() in d for pattern in exclude_patterns if pattern.strip())]
            
            for filename in filenames:
                file_path = os.path.join(root, filename)
                
                # Check include patterns
                if include_patterns and include_patterns != ['']:
                    if not any(self._matches_pattern(filename, pattern.strip()) for pattern in include_patterns):
                        continue
                
                # Check exclude patterns
                if any(pattern.strip() in file_path for pattern in exclude_patterns if pattern.strip()):
                    continue
                
                files.append(file_path)
        
        return files
    
    def _matches_pattern(self, filename: str, pattern: str) -> bool:
        """Check if filename matches pattern (supports wildcards)."""
        import fnmatch
        return fnmatch.fnmatch(filename, pattern)
    
    def _analyze_file(self, file_path: str) -> Optional[FileAnalysis]:
        """Analyze a single file."""
        try:
            ext = Path(file_path).suffix.lower()
            if ext in self.language_analyzers:
                return self.language_analyzers[ext](file_path)
            else:
                return self._analyze_generic_file(file_path)
        except Exception as e:
            logger.warning(f"Failed to analyze {file_path}: {e}")
            return None
    
    def _analyze_python_file(self, file_path: str) -> FileAnalysis:
        """Analyze Python file."""
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        entities = []
        imports = []
        
        try:
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    entities.append(CodeEntity(
                        name=node.name,
                        entity_type="function",
                        file_path=file_path,
                        line_number=node.lineno,
                        end_line=getattr(node, 'end_lineno', None),
                        signature=f"def {node.name}({', '.join(arg.arg for arg in node.args.args)})",
                        docstring=ast.get_docstring(node),
                        complexity_score=self._calculate_complexity(node)
                    ))
                
                elif isinstance(node, ast.ClassDef):
                    entities.append(CodeEntity(
                        name=node.name,
                        entity_type="class",
                        file_path=file_path,
                        line_number=node.lineno,
                        end_line=getattr(node, 'end_lineno', None),
                        signature=f"class {node.name}",
                        docstring=ast.get_docstring(node)
                    ))
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:
                        module = node.module or ""
                        imports.append(module)
        
        except SyntaxError:
            pass  # Skip files with syntax errors
        
        # Calculate metrics
        lines = content.split('\n')
        complexity_metrics = {
            "cyclomatic": sum(entity.complexity_score for entity in entities if entity.entity_type == "function"),
            "lines_of_code": len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            "comment_ratio": len([line for line in lines if line.strip().startswith('#')]) / len(lines) if lines else 0
        }
        
        size_metrics = {
            "total_lines": len(lines),
            "code_lines": complexity_metrics["lines_of_code"],
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "blank_lines": len([line for line in lines if not line.strip()])
        }
        
        return FileAnalysis(
            file_path=file_path,
            language="python",
            entities=entities,
            imports=imports,
            exports=[],  # Python doesn't have explicit exports
            complexity_metrics=complexity_metrics,
            dependencies=imports,
            size_metrics=size_metrics,
            last_modified=str(os.path.getmtime(file_path)),
            analysis_timestamp=str(hash(content))
        )
    
    def _analyze_javascript_file(self, file_path: str) -> FileAnalysis:
        """Analyze JavaScript file (simplified)."""
        return self._analyze_generic_file(file_path, "javascript")
    
    def _analyze_typescript_file(self, file_path: str) -> FileAnalysis:
        """Analyze TypeScript file (simplified)."""
        return self._analyze_generic_file(file_path, "typescript")
    
    def _analyze_java_file(self, file_path: str) -> FileAnalysis:
        """Analyze Java file (simplified)."""
        return self._analyze_generic_file(file_path, "java")
    
    def _analyze_cpp_file(self, file_path: str) -> FileAnalysis:
        """Analyze C++ file (simplified)."""
        return self._analyze_generic_file(file_path, "cpp")
    
    def _analyze_csharp_file(self, file_path: str) -> FileAnalysis:
        """Analyze C# file (simplified)."""
        return self._analyze_generic_file(file_path, "csharp")
    
    def _analyze_go_file(self, file_path: str) -> FileAnalysis:
        """Analyze Go file (simplified)."""
        return self._analyze_generic_file(file_path, "go")
    
    def _analyze_rust_file(self, file_path: str) -> FileAnalysis:
        """Analyze Rust file (simplified)."""
        return self._analyze_generic_file(file_path, "rust")
    
    def _analyze_generic_file(self, file_path: str, language: str = "unknown") -> FileAnalysis:
        """Generic file analysis for unsupported languages."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Basic metrics
            size_metrics = {
                "total_lines": len(lines),
                "code_lines": len([line for line in lines if line.strip()]),
                "blank_lines": len([line for line in lines if not line.strip()])
            }
            
            complexity_metrics = {
                "lines_of_code": size_metrics["code_lines"],
                "cyclomatic": 1  # Default complexity
            }
            
            return FileAnalysis(
                file_path=file_path,
                language=language,
                entities=[],
                imports=[],
                exports=[],
                complexity_metrics=complexity_metrics,
                dependencies=[],
                size_metrics=size_metrics,
                last_modified=str(os.path.getmtime(file_path)),
                analysis_timestamp=str(hash(content))
            )
        
        except Exception:
            return None
    
    def _calculate_complexity(self, node: ast.AST) -> float:
        """Calculate cyclomatic complexity for an AST node."""
        complexity = 1  # Base complexity
        
        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1
        
        return complexity
    
    def _build_project_analysis(self, project_root: str, file_analyses: Dict[str, FileAnalysis]) -> ProjectAnalysis:
        """Build comprehensive project analysis from file analyses."""
        # Build dependency graph
        dependency_graph = {}
        for file_path, analysis in file_analyses.items():
            dependency_graph[file_path] = analysis.dependencies
        
        # Detect technology stack
        technology_stack = set()
        for analysis in file_analyses.values():
            technology_stack.add(analysis.language)
            technology_stack.update(analysis.imports[:5])  # Top 5 imports
        
        # Calculate complexity summary
        total_complexity = sum(analysis.complexity_metrics.get("cyclomatic", 0) for analysis in file_analyses.values())
        total_files = len(file_analyses)
        
        complexity_summary = {
            "total_complexity": total_complexity,
            "average_complexity": total_complexity / total_files if total_files else 0,
            "total_files": total_files,
            "total_lines": sum(analysis.size_metrics.get("total_lines", 0) for analysis in file_analyses.values())
        }
        
        # Identify hotspots (high complexity files)
        avg_complexity = complexity_summary["average_complexity"]
        hotspots = [
            file_path for file_path, analysis in file_analyses.items()
            if analysis.complexity_metrics.get("cyclomatic", 0) > avg_complexity * 2
        ]
        
        return ProjectAnalysis(
            project_root=project_root,
            files=file_analyses,
            dependency_graph=dependency_graph,
            architecture_patterns=[],  # Will be filled by architecture analysis
            technology_stack=list(technology_stack),
            complexity_summary=complexity_summary,
            hotspots=hotspots,
            recommendations=[]
        )
    
    def _find_circular_dependencies(self, dependency_graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Find circular dependencies in the dependency graph."""
        # Simplified cycle detection
        cycles = []
        visited = set()
        
        def dfs(node, path):
            if node in path:
                cycle_start = path.index(node)
                cycles.append(path[cycle_start:] + [node])
                return
            
            if node in visited:
                return
            
            visited.add(node)
            path.append(node)
            
            for neighbor in dependency_graph.get(node, []):
                if neighbor in dependency_graph:  # Only follow internal dependencies
                    dfs(neighbor, path.copy())
        
        for node in dependency_graph:
            if node not in visited:
                dfs(node, [])
        
        return cycles[:10]  # Return first 10 cycles
    
    def _generate_comprehensive_report(self, project_analysis: ProjectAnalysis) -> str:
        """Generate comprehensive analysis report."""
        report_parts = [
            "## 📊 Comprehensive Codebase Analysis",
            f"**Project Root:** {project_analysis.project_root}",
            f"**Files Analyzed:** {project_analysis.complexity_summary['total_files']}",
            f"**Total Lines of Code:** {project_analysis.complexity_summary['total_lines']:,}",
            f"**Average Complexity:** {project_analysis.complexity_summary['average_complexity']:.2f}",
            "",
            "### 🛠️ Technology Stack"
        ]
        
        for tech in sorted(project_analysis.technology_stack)[:10]:
            if tech and tech != "unknown":
                report_parts.append(f"- {tech}")
        
        if project_analysis.hotspots:
            report_parts.append("\n### ⚠️ Complexity Hotspots")
            for hotspot in project_analysis.hotspots[:5]:
                report_parts.append(f"- {os.path.basename(hotspot)}")
        
        return "\n".join(report_parts)
    
    def _generate_recommendations(self, project_analysis: ProjectAnalysis) -> str:
        """Generate improvement recommendations."""
        recommendations = []
        
        # Complexity recommendations
        if project_analysis.complexity_summary["average_complexity"] > 10:
            recommendations.append("Consider refactoring high-complexity functions to improve maintainability")
        
        # Hotspot recommendations
        if len(project_analysis.hotspots) > project_analysis.complexity_summary["total_files"] * 0.1:
            recommendations.append("Multiple complexity hotspots detected - prioritize refactoring efforts")
        
        # Architecture recommendations
        if not project_analysis.architecture_patterns:
            recommendations.append("Consider adopting a clear architectural pattern for better code organization")
        
        # Default recommendations
        if not recommendations:
            recommendations = [
                "Codebase appears well-structured",
                "Consider adding more comprehensive documentation",
                "Regular code reviews can help maintain quality"
            ]
        
        return "\n".join(f"- {rec}" for rec in recommendations)

    def analyze_multi_file_coordination(self, project_analysis: ProjectAnalysis) -> Dict[str, Any]:
        """Analyze multi-file coordination opportunities and issues."""
        coordination_analysis = {
            "cross_file_dependencies": {},
            "shared_interfaces": [],
            "coordination_opportunities": [],
            "potential_issues": [],
            "refactoring_suggestions": []
        }

        # Analyze cross-file dependencies
        for file_path, file_analysis in project_analysis.files.items():
            cross_deps = []
            for entity in file_analysis.entities:
                for dep in entity.dependencies:
                    # Check if dependency is from another file
                    dep_file = self._find_dependency_file(dep, project_analysis)
                    if dep_file and dep_file != file_path:
                        cross_deps.append((dep, dep_file))

            if cross_deps:
                coordination_analysis["cross_file_dependencies"][file_path] = cross_deps

        # Identify shared interfaces
        interface_usage = defaultdict(list)
        for file_path, file_analysis in project_analysis.files.items():
            for entity in file_analysis.entities:
                if entity.entity_type in ["class", "function"] and entity.signature:
                    interface_usage[entity.signature].append(file_path)

        # Find interfaces used across multiple files
        for interface, files in interface_usage.items():
            if len(files) > 1:
                coordination_analysis["shared_interfaces"].append({
                    "interface": interface,
                    "files": files,
                    "usage_count": len(files)
                })

        # Generate coordination opportunities
        coordination_analysis["coordination_opportunities"] = self._identify_coordination_opportunities(
            project_analysis
        )

        # Identify potential issues
        coordination_analysis["potential_issues"] = self._identify_coordination_issues(
            project_analysis
        )

        # Generate refactoring suggestions
        coordination_analysis["refactoring_suggestions"] = self._generate_refactoring_suggestions(
            project_analysis, coordination_analysis
        )

        return coordination_analysis

    def perform_project_reasoning(self, project_analysis: ProjectAnalysis) -> Dict[str, Any]:
        """Perform high-level project reasoning and understanding."""
        reasoning_analysis = {
            "architecture_patterns": [],
            "design_principles": [],
            "project_structure": {},
            "evolution_suggestions": [],
            "architectural_debt": [],
            "scalability_assessment": {}
        }

        # Identify architecture patterns
        reasoning_analysis["architecture_patterns"] = self._identify_architecture_patterns(
            project_analysis
        )

        # Analyze design principles adherence
        reasoning_analysis["design_principles"] = self._analyze_design_principles(
            project_analysis
        )

        # Analyze project structure
        reasoning_analysis["project_structure"] = self._analyze_project_structure(
            project_analysis
        )

        # Generate evolution suggestions
        reasoning_analysis["evolution_suggestions"] = self._generate_evolution_suggestions(
            project_analysis
        )

        # Identify architectural debt
        reasoning_analysis["architectural_debt"] = self._identify_architectural_debt(
            project_analysis
        )

        # Assess scalability
        reasoning_analysis["scalability_assessment"] = self._assess_scalability(
            project_analysis
        )

        return reasoning_analysis

    def _find_dependency_file(self, dependency: str, project_analysis: ProjectAnalysis) -> Optional[str]:
        """Find which file contains a specific dependency."""
        for file_path, file_analysis in project_analysis.files.items():
            for entity in file_analysis.entities:
                if entity.name == dependency:
                    return file_path
        return None

    def _identify_coordination_opportunities(self, project_analysis: ProjectAnalysis) -> List[str]:
        """Identify opportunities for better multi-file coordination."""
        opportunities = []

        # Look for duplicate code patterns across files
        code_patterns = defaultdict(list)
        for file_path, file_analysis in project_analysis.files.items():
            for entity in file_analysis.entities:
                if entity.signature:
                    pattern_key = self._extract_pattern_signature(entity.signature)
                    code_patterns[pattern_key].append((file_path, entity.name))

        for pattern, occurrences in code_patterns.items():
            if len(occurrences) > 1:
                files = [occ[0] for occ in occurrences]
                opportunities.append(
                    f"Similar patterns found in {len(files)} files: {', '.join(set(files))}"
                )

        return opportunities[:5]  # Limit to top 5

    def _identify_coordination_issues(self, project_analysis: ProjectAnalysis) -> List[str]:
        """Identify potential coordination issues."""
        issues = []

        # Check for circular dependencies
        circular_deps = self._find_circular_dependencies(project_analysis.dependency_graph)
        if circular_deps:
            issues.append(f"Found {len(circular_deps)} circular dependencies")

        # Check for high coupling
        high_coupling_files = []
        for file_path, deps in project_analysis.dependency_graph.items():
            if len(deps) > 10:  # Arbitrary threshold
                high_coupling_files.append(file_path)

        if high_coupling_files:
            issues.append(f"High coupling detected in {len(high_coupling_files)} files")

        return issues

    def _generate_refactoring_suggestions(
        self,
        project_analysis: ProjectAnalysis,
        coordination_analysis: Dict[str, Any]
    ) -> List[str]:
        """Generate refactoring suggestions based on coordination analysis."""
        suggestions = []

        # Suggest extracting common interfaces
        shared_interfaces = coordination_analysis.get("shared_interfaces", [])
        if len(shared_interfaces) > 3:
            suggestions.append("Consider extracting common interfaces into a shared module")

        # Suggest reducing coupling
        cross_deps = coordination_analysis.get("cross_file_dependencies", {})
        high_coupling_files = [f for f, deps in cross_deps.items() if len(deps) > 5]
        if high_coupling_files:
            suggestions.append(f"Consider reducing coupling in {len(high_coupling_files)} files")

        return suggestions

    def _extract_pattern_signature(self, signature: str) -> str:
        """Extract a pattern signature for similarity comparison."""
        # Simplified pattern extraction - remove specific names but keep structure
        import re
        pattern = re.sub(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', 'VAR', signature)
        pattern = re.sub(r'\d+', 'NUM', pattern)
        return pattern

    def _identify_architecture_patterns(self, project_analysis: ProjectAnalysis) -> List[str]:
        """Identify architectural patterns in the project."""
        patterns = []

        # Look for MVC pattern
        has_models = any("model" in file.lower() for file in project_analysis.files.keys())
        has_views = any("view" in file.lower() for file in project_analysis.files.keys())
        has_controllers = any("controller" in file.lower() for file in project_analysis.files.keys())

        if has_models and has_views and has_controllers:
            patterns.append("MVC (Model-View-Controller)")

        # Look for layered architecture
        has_service_layer = any("service" in file.lower() for file in project_analysis.files.keys())
        has_data_layer = any("data" in file.lower() or "repository" in file.lower()
                           for file in project_analysis.files.keys())

        if has_service_layer and has_data_layer:
            patterns.append("Layered Architecture")

        # Look for microservices indicators
        has_api_files = any("api" in file.lower() for file in project_analysis.files.keys())
        has_config_files = any("config" in file.lower() for file in project_analysis.files.keys())

        if has_api_files and has_config_files and len(project_analysis.files) > 20:
            patterns.append("Microservices Architecture")

        return patterns

    def _analyze_design_principles(self, project_analysis: ProjectAnalysis) -> List[str]:
        """Analyze adherence to design principles."""
        principles = []

        # Single Responsibility Principle
        large_classes = []
        for file_path, file_analysis in project_analysis.files.items():
            for entity in file_analysis.entities:
                if (entity.entity_type == "class" and
                    entity.metadata.get("method_count", 0) > 10):
                    large_classes.append(entity.name)

        if large_classes:
            principles.append(f"SRP: {len(large_classes)} classes may violate Single Responsibility")
        else:
            principles.append("SRP: Good adherence to Single Responsibility Principle")

        # DRY Principle
        duplicate_patterns = self._find_duplicate_patterns(project_analysis)
        if duplicate_patterns > 5:
            principles.append(f"DRY: {duplicate_patterns} potential code duplications found")
        else:
            principles.append("DRY: Good adherence to Don't Repeat Yourself principle")

        return principles

    def _analyze_project_structure(self, project_analysis: ProjectAnalysis) -> Dict[str, Any]:
        """Analyze the overall project structure."""
        structure = {
            "directory_depth": 0,
            "file_distribution": {},
            "naming_conventions": {},
            "organization_score": 0.0
        }

        # Calculate directory depth
        max_depth = 0
        for file_path in project_analysis.files.keys():
            depth = len(Path(file_path).parts)
            max_depth = max(max_depth, depth)
        structure["directory_depth"] = max_depth

        # Analyze file distribution
        dir_counts = defaultdict(int)
        for file_path in project_analysis.files.keys():
            directory = str(Path(file_path).parent)
            dir_counts[directory] += 1

        structure["file_distribution"] = dict(dir_counts)

        # Analyze naming conventions
        naming_patterns = {
            "snake_case": 0,
            "camelCase": 0,
            "PascalCase": 0,
            "kebab-case": 0
        }

        for file_path in project_analysis.files.keys():
            filename = Path(file_path).stem
            if "_" in filename and filename.islower():
                naming_patterns["snake_case"] += 1
            elif any(c.isupper() for c in filename[1:]) and filename[0].islower():
                naming_patterns["camelCase"] += 1
            elif filename[0].isupper():
                naming_patterns["PascalCase"] += 1
            elif "-" in filename:
                naming_patterns["kebab-case"] += 1

        structure["naming_conventions"] = naming_patterns

        # Calculate organization score (0-1)
        total_files = len(project_analysis.files)
        if total_files > 0:
            # Penalize too deep or too shallow structures
            depth_score = max(0, 1 - abs(max_depth - 4) * 0.1)  # Ideal depth around 4

            # Reward consistent naming
            max_naming = max(naming_patterns.values()) if naming_patterns.values() else 0
            naming_score = max_naming / total_files if total_files > 0 else 0

            structure["organization_score"] = (depth_score + naming_score) / 2

        return structure

    def _generate_evolution_suggestions(self, project_analysis: ProjectAnalysis) -> List[str]:
        """Generate suggestions for project evolution."""
        suggestions = []

        total_files = len(project_analysis.files)
        avg_complexity = project_analysis.complexity_summary.get("average_complexity", 0)

        # Size-based suggestions
        if total_files > 100:
            suggestions.append("Consider modularizing the project into smaller, focused packages")
        elif total_files < 5:
            suggestions.append("Project structure could benefit from better organization as it grows")

        # Complexity-based suggestions
        if avg_complexity > 15:
            suggestions.append("High complexity detected - consider implementing design patterns")

        # Technology suggestions
        tech_stack = project_analysis.technology_stack
        if "python" in tech_stack and total_files > 20:
            suggestions.append("Consider adding type hints for better code maintainability")

        return suggestions

    def _identify_architectural_debt(self, project_analysis: ProjectAnalysis) -> List[str]:
        """Identify architectural debt in the project."""
        debt_items = []

        # Look for god classes/files
        for file_path, file_analysis in project_analysis.files.items():
            if len(file_analysis.entities) > 20:
                debt_items.append(f"God file detected: {os.path.basename(file_path)}")

        # Look for tight coupling
        high_coupling = []
        for file_path, deps in project_analysis.dependency_graph.items():
            if len(deps) > 15:
                high_coupling.append(os.path.basename(file_path))

        if high_coupling:
            debt_items.append(f"High coupling in {len(high_coupling)} files")

        return debt_items

    def _assess_scalability(self, project_analysis: ProjectAnalysis) -> Dict[str, Any]:
        """Assess project scalability."""
        assessment = {
            "current_scale": "small",
            "bottlenecks": [],
            "scalability_score": 0.0,
            "recommendations": []
        }

        total_files = len(project_analysis.files)
        total_lines = project_analysis.complexity_summary.get("total_lines", 0)

        # Determine current scale
        if total_files > 100 or total_lines > 50000:
            assessment["current_scale"] = "large"
        elif total_files > 20 or total_lines > 10000:
            assessment["current_scale"] = "medium"

        # Identify bottlenecks
        if project_analysis.hotspots:
            assessment["bottlenecks"] = [
                f"Complexity hotspot: {os.path.basename(hotspot)}"
                for hotspot in project_analysis.hotspots[:3]
            ]

        # Calculate scalability score
        complexity_factor = max(0, 1 - (project_analysis.complexity_summary.get("average_complexity", 0) - 5) * 0.05)
        coupling_factor = max(0, 1 - len(project_analysis.dependency_graph) * 0.01)
        assessment["scalability_score"] = (complexity_factor + coupling_factor) / 2

        # Generate recommendations
        if assessment["scalability_score"] < 0.7:
            assessment["recommendations"].append("Consider refactoring to improve scalability")
        if len(assessment["bottlenecks"]) > 2:
            assessment["recommendations"].append("Address complexity hotspots before scaling")

        return assessment

    def _find_duplicate_patterns(self, project_analysis: ProjectAnalysis) -> int:
        """Find duplicate code patterns across the project."""
        patterns = defaultdict(int)

        for file_analysis in project_analysis.files.values():
            for entity in file_analysis.entities:
                if entity.signature:
                    pattern = self._extract_pattern_signature(entity.signature)
                    patterns[pattern] += 1

        # Count patterns that appear more than once
        duplicates = sum(1 for count in patterns.values() if count > 1)
        return duplicates


# Register the tool
def register_tool():
    """Register the advanced codebase analyzer."""
    try:
        from tools.registry import get_registry
        registry = get_registry()
        registry.register_tool(AdvancedCodebaseAnalyzer())
    except ImportError:
        # Registry not available, skip registration
        pass


# Auto-register when imported
register_tool()
