"""
Modern Chat Interface for Enhanced AI Coding Agent.

This module provides a beautiful chat interface with real-time messaging,
syntax highlighting for code blocks, and modern styling.
"""

import asyncio
import logging
import threading
from typing import Optional, Dict, Any, List, Callable, Generator
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.markdown import Markdown
    from rich.syntax import Syntax
    from rich.table import Table
    from rich.columns import Columns
    from rich.align import Align
    from rich.live import Live
    from rich.layout import Layout
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

from message import Message
from ui.syntax_highlighter import SyntaxHighlighter

logger = logging.getLogger(__name__)


class MessageType(Enum):
    """Types of chat messages."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    ERROR = "error"
    CODE = "code"
    TOOL = "tool"


@dataclass
class ChatMessage:
    """Chat message with enhanced metadata."""
    content: str
    message_type: MessageType
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    code_language: Optional[str] = None
    is_streaming: bool = False
    message_id: Optional[str] = None


class ChatInterface:
    """
    Modern chat interface with real-time messaging and syntax highlighting.
    
    Features:
    - Real-time message streaming
    - Syntax highlighting for code blocks
    - Message history management
    - Modern styling with themes
    - Typing indicators
    - Message reactions and interactions
    """
    
    def __init__(
        self,
        theme: str = "dark",
        history_limit: int = 1000,
        auto_scroll: bool = True,
        show_timestamps: bool = True
    ):
        """
        Initialize the chat interface.
        
        Args:
            theme: UI theme
            history_limit: Maximum number of messages to keep
            auto_scroll: Whether to auto-scroll to new messages
            show_timestamps: Whether to show message timestamps
        """
        self.theme = theme
        self.history_limit = history_limit
        self.auto_scroll = auto_scroll
        self.show_timestamps = show_timestamps
        
        self.console = Console() if RICH_AVAILABLE else None
        self.syntax_highlighter = SyntaxHighlighter(theme=theme)
        
        self.messages: List[ChatMessage] = []
        self.message_callbacks: List[Callable[[ChatMessage], None]] = []
        self.typing_indicator_active = False
        
        self._lock = threading.Lock()
        
        # Theme colors
        self._setup_theme_colors()
    
    def _setup_theme_colors(self) -> None:
        """Setup theme-specific colors."""
        if self.theme == "dark":
            self.colors = {
                "user": "#58a6ff",      # Blue
                "assistant": "#7c3aed",  # Purple
                "system": "#6b7280",     # Gray
                "error": "#f87171",      # Red
                "code": "#34d399",       # Green
                "tool": "#fbbf24",       # Yellow
                "background": "#0d1117", # Dark background
                "border": "#30363d",     # Border
                "text": "#f0f6fc"        # Text
            }
        elif self.theme == "light":
            self.colors = {
                "user": "#0969da",       # Blue
                "assistant": "#8250df",  # Purple
                "system": "#656d76",     # Gray
                "error": "#d1242f",      # Red
                "code": "#1a7f37",       # Green
                "tool": "#bf8700",       # Yellow
                "background": "#ffffff", # Light background
                "border": "#d0d7de",     # Border
                "text": "#24292f"        # Text
            }
        else:  # claude theme
            self.colors = {
                "user": "#2563eb",       # Blue
                "assistant": "#7c2d12",  # Brown/Orange
                "system": "#64748b",     # Slate
                "error": "#dc2626",      # Red
                "code": "#059669",       # Emerald
                "tool": "#d97706",       # Amber
                "background": "#fefefe", # Off-white
                "border": "#e2e8f0",     # Light border
                "text": "#1e293b"        # Dark text
            }
    
    def add_message(
        self,
        content: str,
        message_type: MessageType = MessageType.USER,
        metadata: Optional[Dict[str, Any]] = None,
        code_language: Optional[str] = None
    ) -> str:
        """
        Add a message to the chat.
        
        Args:
            content: Message content
            message_type: Type of message
            metadata: Additional metadata
            code_language: Programming language for code blocks
        
        Returns:
            Message ID
        """
        with self._lock:
            message = ChatMessage(
                content=content,
                message_type=message_type,
                metadata=metadata or {},
                code_language=code_language,
                message_id=f"msg_{len(self.messages)}"
            )
            
            self.messages.append(message)
            
            # Trim history if needed
            if len(self.messages) > self.history_limit:
                self.messages = self.messages[-self.history_limit:]
            
            # Notify callbacks
            for callback in self.message_callbacks:
                try:
                    callback(message)
                except Exception as e:
                    logger.error(f"Error in message callback: {e}")
            
            return message.message_id
    
    def add_streaming_message(
        self,
        message_generator: Generator[str, None, None],
        message_type: MessageType = MessageType.ASSISTANT
    ) -> str:
        """
        Add a streaming message to the chat.
        
        Args:
            message_generator: Generator yielding message chunks
            message_type: Type of message
        
        Returns:
            Message ID
        """
        message_id = f"msg_{len(self.messages)}_streaming"
        
        def stream_handler():
            content = ""
            for chunk in message_generator:
                content += chunk
                # Update the streaming message
                self._update_streaming_message(message_id, content)
            
            # Finalize the message
            self._finalize_streaming_message(message_id, content, message_type)
        
        # Start streaming in a separate thread
        threading.Thread(target=stream_handler, daemon=True).start()
        
        return message_id
    
    def _update_streaming_message(self, message_id: str, content: str) -> None:
        """Update a streaming message."""
        # Find and update the streaming message
        with self._lock:
            for message in self.messages:
                if message.message_id == message_id:
                    message.content = content
                    message.is_streaming = True
                    break
            else:
                # Create new streaming message
                message = ChatMessage(
                    content=content,
                    message_type=MessageType.ASSISTANT,
                    is_streaming=True,
                    message_id=message_id
                )
                self.messages.append(message)
    
    def _finalize_streaming_message(
        self,
        message_id: str,
        content: str,
        message_type: MessageType
    ) -> None:
        """Finalize a streaming message."""
        with self._lock:
            for message in self.messages:
                if message.message_id == message_id:
                    message.content = content
                    message.message_type = message_type
                    message.is_streaming = False
                    break
    
    def format_message(self, message: ChatMessage) -> Panel:
        """
        Format a message for display.
        
        Args:
            message: Message to format
        
        Returns:
            Formatted Rich Panel
        """
        if not RICH_AVAILABLE:
            return f"[{message.message_type.value}] {message.content}"
        
        # Get color for message type
        color = self.colors.get(message.message_type.value, self.colors["text"])
        
        # Format content
        if message.code_language and self.syntax_highlighter:
            # Syntax highlighted code
            content = self.syntax_highlighter.highlight_code(
                message.content,
                message.code_language
            )
        elif "```" in message.content:
            # Markdown with code blocks
            content = Markdown(message.content)
        else:
            # Plain text
            content = Text(message.content, style=color)
        
        # Create timestamp
        timestamp = ""
        if self.show_timestamps:
            timestamp = message.timestamp.strftime("%H:%M:%S")
        
        # Create title
        title = f"{message.message_type.value.title()}"
        if timestamp:
            title += f" • {timestamp}"
        
        # Add streaming indicator
        if message.is_streaming:
            title += " • ⚡ Streaming..."
        
        # Create panel
        panel = Panel(
            content,
            title=title,
            title_align="left",
            border_style=color,
            padding=(0, 1)
        )
        
        return panel
    
    def render_chat(self) -> Layout:
        """
        Render the entire chat interface.
        
        Returns:
            Rich Layout with chat messages
        """
        if not RICH_AVAILABLE:
            return "\n".join([f"[{msg.message_type.value}] {msg.content}" 
                            for msg in self.messages])
        
        layout = Layout()
        
        # Create message panels
        message_panels = []
        for message in self.messages[-20:]:  # Show last 20 messages
            panel = self.format_message(message)
            message_panels.append(panel)
        
        # Create chat area
        if message_panels:
            chat_content = Columns(message_panels, equal=False, expand=True)
        else:
            chat_content = Panel(
                Align.center("💬 Start a conversation!"),
                title="Chat",
                border_style=self.colors["border"]
            )
        
        layout.update(chat_content)
        return layout
    
    def clear_chat(self) -> None:
        """Clear all chat messages."""
        with self._lock:
            self.messages.clear()
    
    def export_chat(self, format: str = "markdown") -> str:
        """
        Export chat history.
        
        Args:
            format: Export format ('markdown', 'json', 'text')
        
        Returns:
            Exported chat content
        """
        if format == "markdown":
            return self._export_markdown()
        elif format == "json":
            return self._export_json()
        else:
            return self._export_text()
    
    def _export_markdown(self) -> str:
        """Export chat as markdown."""
        lines = ["# Chat History\n"]
        
        for message in self.messages:
            timestamp = message.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            lines.append(f"## {message.message_type.value.title()} - {timestamp}\n")
            lines.append(f"{message.content}\n")
        
        return "\n".join(lines)
    
    def _export_json(self) -> str:
        """Export chat as JSON."""
        import json
        
        data = []
        for message in self.messages:
            data.append({
                "content": message.content,
                "type": message.message_type.value,
                "timestamp": message.timestamp.isoformat(),
                "metadata": message.metadata
            })
        
        return json.dumps(data, indent=2)
    
    def _export_text(self) -> str:
        """Export chat as plain text."""
        lines = []
        
        for message in self.messages:
            timestamp = message.timestamp.strftime("%H:%M:%S")
            lines.append(f"[{timestamp}] {message.message_type.value}: {message.content}")
        
        return "\n".join(lines)
    
    def add_message_callback(self, callback: Callable[[ChatMessage], None]) -> None:
        """Add a callback for new messages."""
        self.message_callbacks.append(callback)
    
    def remove_message_callback(self, callback: Callable[[ChatMessage], None]) -> None:
        """Remove a message callback."""
        if callback in self.message_callbacks:
            self.message_callbacks.remove(callback)
